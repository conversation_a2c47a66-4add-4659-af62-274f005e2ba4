import{S as Q,_ as T}from"./index-CkIekciI.js";import{l as M,E as v,j as R}from"./element-plus-DZBbDeaO.js";import{u as S}from"./vue-router-e9iWfNxP.js";import{d as N,c as I,a as r,b as t,D as F,K as g,I as $,J as V,o as n,r as p,s as L,C as w,F as q,a1 as x,E as j,q as B}from"./vue-vendor-2E6AJATX.js";import{u as z,a as E}from"./utils-common-CvYGMv_l.js";import{u as D}from"./app-stores-DSLz6__G.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";import"./element-icons-CT2GCbaF.js";import"./vendor-lodash-BPsl90Nm.js";const U={class:"product-image"},J=["src","alt"],K={class:"product-info"},G={class:"product-name"},H={class:"price-view-container"},O={class:"price-section"},X={class:"current-price"},Y={class:"price-integer"},Z={class:"price-decimal"},ee={class:"price-label"},te={class:"view-section"},ae={class:"view-count"},se={class:"view-label"},ne={class:"time-button-container"},oe={class:"time-section"},ie={class:"time-info"},re={class:"time-label"},ce={class:"time-value"},le={class:"schedule-info"},de={class:"schedule-label"},ue={class:"schedule-value"},pe={class:"button-section"},_e=N({__name:"BiddingCard",props:{buttonType:{default:"record"},id:{},pmhId:{default:0},bdName:{},startTime:{},endTime:{},bdPic:{},bdQipaijia:{},qpjDanwie:{},bdWeiguan:{},timeLabel:{default:"截止报名"},scheduleLabel:{default:"预计开始"},status:{default:0}},emits:["cardClick","buttonClick"],setup(P,{emit:m}){const l=P,d=m,_=I(()=>l.status===1),c=I(()=>l.status===6),b=I(()=>l.status===1&&l.buttonType==="record"),u=I(()=>{switch(l.status){case 1:return"即将开始";case 5:return"交易完成";case 6:return"核实中";default:return"未知状态"}}),y=I(()=>l.buttonType==="cancel"?"取消收藏":"出价记录"),f=I(()=>Math.floor(Number(l.bdQipaijia))),k=I(()=>((Number(l.bdQipaijia)-Math.floor(Number(l.bdQipaijia)))*100).toFixed(0).padStart(2,"0")),C=()=>{d("cardClick",{productId:l.id.toString(),pmhId:l.pmhId.toString()})},a=()=>{d("buttonClick",{productId:l.id.toString(),pmhId:l.pmhId.toString(),type:l.buttonType})};return(e,o)=>{const i=Q;return n(),r("div",{class:"auction-card",onClick:C},[t("div",{class:F(["status-tag",{"status-active":_.value,"status-verifying":c.value}])},[t("span",null,g(u.value),1)],2),t("div",U,[t("img",{src:e.bdPic,alt:e.bdName},null,8,J)]),t("div",K,[t("span",G,g(e.bdName),1),t("div",H,[t("div",O,[t("div",X,[t("span",Y,g(f.value),1),t("span",Z,"."+g(k.value),1)]),t("div",ee,[$(i,{iconName:"auction-action",class:"price-icon"}),t("span",null,g(e.qpjDanwie),1)])]),o[1]||(o[1]=t("div",{class:"divider"},null,-1)),t("div",te,[t("div",ae,g(e.bdWeiguan),1),t("div",se,[$(i,{iconName:"auction-view",class:"view-icon"}),o[0]||(o[0]=t("span",null,"围观(次)",-1))])])]),t("div",ne,[t("div",oe,[t("div",ie,[t("span",re,g(e.timeLabel),1),t("span",ce,g(e.endTime),1)]),t("div",le,[t("span",de,g(e.scheduleLabel),1),t("span",ue,g(e.startTime),1)])]),t("div",pe,[t("div",{class:F(["action-button",{"button-active":b.value,"button-cancel":e.buttonType==="cancel"}]),onClick:V(a,["stop"])},[t("span",null,g(y.value),1),$(i,{iconName:"auction-arrows-right",class:"button-icon"})],2)])])])])}}}),A=T(_e,[["__scopeId","data-v-c77d0db3"]]),ve={class:"my-favorites"},me={class:"page-header"},ge={key:0,class:"loading-container"},be={key:1,class:"empty-container"},ye={key:2,class:"auction-grid"},he={key:3,class:"pagination-container"},fe=N({__name:"MyFavorites",setup(P){const m=S(),l=D(),d=p(!1),_=p([]),c=p(1),b=p(12),u=p(0),y=async()=>{try{d.value=!0;const a=l.userInfo;if(!(a!=null&&a.id)){v.error("请先登录");return}const e={member_id:a.id,page:c.value,type:1},o=await z.getMyFavoriteAuctions(e);if(o.code===1){const i=o.data.data.map(async s=>({id:s.id,pmhId:s.pmh_id||0,bdName:s.bd_title,startTime:s.start_time_name.split("年")[1],endTime:s.start_time_name.split("年")[1],bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+s.bd_url,bdQipaijia:s.bd_qipaijia,qpjDanwie:s.qpj_danwie,bdWeiguan:s.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:s.bd_status})),h=await Promise.all(i);_.value=h,u.value=o.data.total||0}else v.error(o.msg||"获取收藏列表失败")}catch(a){console.error("获取收藏列表失败:",a),v.error("获取收藏列表失败")}finally{d.value=!1}},f=a=>{c.value=a,y()},k=a=>{m.push({name:"auctionDetail",query:{id:a.productId,pmhId:a.pmhId}})},C=async a=>{var e;try{if(await R.confirm("确定要取消收藏这个标的吗？","取消收藏",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})==="confirm"){const i={member_id:((e=l.userInfo)==null?void 0:e.id)||0,bd_id:Number(a.productId),isquxiao:0},h=await E.favoriteTarget(i);h.code===1?(v.success("取消收藏成功"),y()):v.error(h.msg||"取消收藏失败")}}catch{}};return L(()=>{y()}),(a,e)=>{const o=M;return n(),r("div",ve,[t("div",me,[e[1]||(e[1]=t("h2",null,"我的收藏",-1)),t("p",null,"共 "+g(u.value)+" 条收藏记录",1)]),d.value?(n(),r("div",ge,e[2]||(e[2]=[t("div",{class:"loading-text"},"加载中...",-1)]))):!d.value&&_.value.length===0?(n(),r("div",be,e[3]||(e[3]=[t("div",{class:"empty-text"},"暂无收藏记录",-1)]))):(n(),r("div",ye,[(n(!0),r(q,null,x(_.value,i=>(n(),j(A,B({key:i.id},{ref_for:!0},i,{"button-type":"cancel",onCardClick:k,onButtonClick:C}),null,16))),128))])),u.value>0?(n(),r("div",he,[$(o,{"current-page":c.value,"onUpdate:currentPage":e[0]||(e[0]=i=>c.value=i),"page-size":b.value,total:u.value,layout:"prev, pager, next, jumper",onCurrentChange:f},null,8,["current-page","page-size","total"])])):w("",!0)])}}}),ke=T(fe,[["__scopeId","data-v-755c9ce1"]]),Ce={class:"my-registrations"},we={class:"page-header"},je={key:0,class:"loading-container"},$e={key:1,class:"empty-container"},Ie={key:2,class:"auction-grid"},Pe={key:3,class:"pagination-container"},Te=N({__name:"MyRegistrations",setup(P){const m=S(),l=D(),d=p(!1),_=p([]),c=p(1),b=p(12),u=p(0),y=async()=>{try{d.value=!0;const a=l.userInfo;if(!(a!=null&&a.id)){v.error("请先登录");return}const e={member_id:a.id,page:c.value,type:1},o=await z.getMyRegisteredAuctions(e);if(o.code===1){const i=o.data.data.map(async s=>({id:s.id,pmhId:s.pmh_id||0,bdName:s.bd_title,startTime:s.start_time_name,endTime:s.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+s.bd_url,bdQipaijia:s.bd_qipaijia,qpjDanwie:s.qpj_danwie,bdWeiguan:s.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:s.bd_status})),h=await Promise.all(i);_.value=h,u.value=o.data.total||0}else v.error(o.msg||"获取报名列表失败")}catch(a){console.error("获取报名列表失败:",a),v.error("获取报名列表失败")}finally{d.value=!1}},f=a=>{c.value=a,y()},k=a=>{m.push({name:"auctionDetail",query:{id:a.productId}})},C=a=>{v.info("查看出价记录功能待实现")};return L(()=>{y()}),(a,e)=>{const o=M;return n(),r("div",Ce,[t("div",we,[e[1]||(e[1]=t("h2",null,"我的报名",-1)),t("p",null,"共 "+g(u.value)+" 条报名记录",1)]),d.value?(n(),r("div",je,e[2]||(e[2]=[t("div",{class:"loading-text"},"加载中...",-1)]))):!d.value&&_.value.length===0?(n(),r("div",$e,e[3]||(e[3]=[t("div",{class:"empty-text"},"暂无报名记录",-1)]))):(n(),r("div",Ie,[(n(!0),r(q,null,x(_.value,i=>(n(),j(A,B({key:i.id},{ref_for:!0},i,{"button-type":"record",onCardClick:k,onButtonClick:C}),null,16))),128))])),u.value>0?(n(),r("div",Pe,[$(o,{"current-page":c.value,"onUpdate:currentPage":e[0]||(e[0]=i=>c.value=i),"page-size":b.value,total:u.value,layout:"prev, pager, next, jumper",onCurrentChange:f},null,8,["current-page","page-size","total"])])):w("",!0)])}}}),Ne=T(Te,[["__scopeId","data-v-c3a23dd9"]]),qe={class:"my-participations"},xe={class:"page-header"},Me={key:0,class:"loading-container"},Se={key:1,class:"empty-container"},Le={key:2,class:"auction-grid"},Be={key:3,class:"pagination-container"},ze=N({__name:"MyParticipations",setup(P){const m=S(),l=D(),d=p(!1),_=p([]),c=p(1),b=p(12),u=p(0),y=async()=>{try{d.value=!0;const a=l.userInfo;if(!(a!=null&&a.id)){v.error("请先登录");return}const e={member_id:a.id,page:c.value,type:1},o=await z.getMyParticipatedAuctions(e);if(o.code===1){const i=o.data.data.map(async s=>({id:s.id,pmhId:s.pmh_id||0,bdName:s.bd_title,startTime:s.start_time_name,endTime:s.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+s.bd_url,bdQipaijia:s.bd_qipaijia,qpjDanwie:s.qpj_danwie,bdWeiguan:s.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:s.bd_status})),h=await Promise.all(i);_.value=h,u.value=o.data.total||0}else v.error(o.msg||"获取参与列表失败")}catch(a){console.error("获取参与列表失败:",a),v.error("获取参与列表失败")}finally{d.value=!1}},f=a=>{c.value=a,y()},k=a=>{m.push({name:"auctionDetail",query:{id:a.productId}})},C=a=>{v.info("查看出价记录功能待实现")};return L(()=>{y()}),(a,e)=>{const o=M;return n(),r("div",qe,[t("div",xe,[e[1]||(e[1]=t("h2",null,"参与标的",-1)),t("p",null,"共 "+g(u.value)+" 条参与记录",1)]),d.value?(n(),r("div",Me,e[2]||(e[2]=[t("div",{class:"loading-text"},"加载中...",-1)]))):!d.value&&_.value.length===0?(n(),r("div",Se,e[3]||(e[3]=[t("div",{class:"empty-text"},"暂无参与记录",-1)]))):(n(),r("div",Le,[(n(!0),r(q,null,x(_.value,i=>(n(),j(A,B({key:i.id},{ref_for:!0},i,{"button-type":"record",onCardClick:k,onButtonClick:C}),null,16))),128))])),u.value>0?(n(),r("div",Be,[$(o,{"current-page":c.value,"onUpdate:currentPage":e[0]||(e[0]=i=>c.value=i),"page-size":b.value,total:u.value,layout:"prev, pager, next, jumper",onCurrentChange:f},null,8,["current-page","page-size","total"])])):w("",!0)])}}}),De=T(ze,[["__scopeId","data-v-2c7d30c8"]]),Ae={class:"my-won-auctions"},We={class:"page-header"},Fe={key:0,class:"loading-container"},Qe={key:1,class:"empty-container"},Re={key:2,class:"auction-grid"},Ve={key:3,class:"pagination-container"},Ee=N({__name:"MyWonAuctions",setup(P){const m=S(),l=D(),d=p(!1),_=p([]),c=p(1),b=p(12),u=p(0),y=async()=>{try{d.value=!0;const a=l.userInfo;if(!(a!=null&&a.id)){v.error("请先登录");return}const e={member_id:a.id,page:c.value,type:1},o=await z.getMyWonAuctions(e);if(o.code===1){const i=o.data.data.map(async s=>({id:s.id,pmhId:s.pmh_id||0,bdName:s.bd_title,startTime:s.start_time_name,endTime:s.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+s.bd_url,bdQipaijia:s.bd_qipaijia,qpjDanwie:s.qpj_danwie,bdWeiguan:s.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:s.bd_status})),h=await Promise.all(i);_.value=h,u.value=o.data.total||0}else v.error(o.msg||"获取竞得列表失败")}catch(a){console.error("获取竞得列表失败:",a),v.error("获取竞得列表失败")}finally{d.value=!1}},f=a=>{c.value=a,y()},k=a=>{m.push({name:"auctionDetail",query:{id:a.productId}})},C=a=>{v.info("查看出价记录功能待实现")};return L(()=>{y()}),(a,e)=>{const o=M;return n(),r("div",Ae,[t("div",We,[e[1]||(e[1]=t("h2",null,"竞得标的",-1)),t("p",null,"共 "+g(u.value)+" 条竞得记录",1)]),d.value?(n(),r("div",Fe,e[2]||(e[2]=[t("div",{class:"loading-text"},"加载中...",-1)]))):!d.value&&_.value.length===0?(n(),r("div",Qe,e[3]||(e[3]=[t("div",{class:"empty-text"},"暂无竞得记录",-1)]))):(n(),r("div",Re,[(n(!0),r(q,null,x(_.value,i=>(n(),j(A,B({key:i.id},{ref_for:!0},i,{"button-type":"record",onCardClick:k,onButtonClick:C}),null,16))),128))])),u.value>0?(n(),r("div",Ve,[$(o,{"current-page":c.value,"onUpdate:currentPage":e[0]||(e[0]=i=>c.value=i),"page-size":b.value,total:u.value,layout:"prev, pager, next, jumper",onCurrentChange:f},null,8,["current-page","page-size","total"])])):w("",!0)])}}}),Ue=T(Ee,[["__scopeId","data-v-2613a076"]]),Je={class:"bidding-manage-container"},Ke={class:"bidding-sidebar"},Ge={class:"sidebar-nav"},He=["onClick"],Oe={class:"nav-text"},Xe={class:"bidding-content"},Ye=N({__name:"index",setup(P){const m=p("favorites"),l=[{key:"favorites",label:"我的收藏",icon:"login-data-attention"},{key:"registrations",label:"我的报名",icon:"login-data-real-name"},{key:"participations",label:"参与标的",icon:"login-data-security"},{key:"won",label:"竞得标的",icon:"login-data-personal"}],d=_=>{m.value=_};return(_,c)=>(n(),r("div",Je,[t("div",Ke,[c[0]||(c[0]=t("div",{class:"sidebar-title"},"竞拍管理",-1)),c[1]||(c[1]=t("div",{class:"sidebar-divider"},null,-1)),t("div",Ge,[(n(),r(q,null,x(l,b=>t("div",{key:b.key,class:F(["nav-item",{active:m.value===b.key}]),onClick:u=>d(b.key)},[$(Q,{iconName:b.icon,class:"nav-icon"},null,8,["iconName"]),t("span",Oe,g(b.label),1)],10,He)),64))])]),t("div",Xe,[m.value==="favorites"?(n(),j(ke,{key:0})):w("",!0),m.value==="registrations"?(n(),j(Ne,{key:1})):w("",!0),m.value==="participations"?(n(),j(De,{key:2})):w("",!0),m.value==="won"?(n(),j(Ue,{key:3})):w("",!0)])]))}}),mt=T(Ye,[["__scopeId","data-v-4f730a4d"]]);export{mt as default};
