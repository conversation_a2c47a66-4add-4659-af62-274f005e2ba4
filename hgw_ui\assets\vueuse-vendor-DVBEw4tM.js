import{c as U,p as J}from"./vendor-others-D4FIkGzs.js";import{au as G,ag as q,y as K,af as Z,Z as W,g as H,s as k,z as X,r as g,u as Y,t as O,c as L}from"./vue-vendor-2E6AJATX.js";function xe(e,n){const t=U({});return J(t)}var ee=Object.defineProperty,te=Object.defineProperties,ne=Object.getOwnPropertyDescriptors,j=Object.getOwnPropertySymbols,re=Object.prototype.hasOwnProperty,oe=Object.prototype.propertyIsEnumerable,x=(e,n,t)=>n in e?ee(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,ae=(e,n)=>{for(var t in n||(n={}))re.call(n,t)&&x(e,t,n[t]);if(j)for(var t of j(n))oe.call(n,t)&&x(e,t,n[t]);return e},se=(e,n)=>te(e,ne(n));function Ae(e,n){var t;const r=K();return Z(()=>{r.value=e()},se(ae({},n),{flush:(t=void 0)!=null?t:"sync"})),W(r)}var A;const T=typeof window<"u",ie=e=>typeof e<"u",ue=e=>typeof e=="function",le=e=>typeof e=="string",b=()=>{},ce=T&&((A=window==null?void 0:window.navigator)==null?void 0:A.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function _(e){return typeof e=="function"?e():Y(e)}function B(e,n){function t(...r){return new Promise((o,s)=>{Promise.resolve(e(()=>n.apply(this,r),{fn:n,thisArg:this,args:r})).then(o).catch(s)})}return t}function fe(e,n={}){let t,r,o=b;const s=u=>{clearTimeout(u),o(),o=b};return u=>{const l=_(e),d=_(n.maxWait);return t&&s(t),l<=0||d!==void 0&&d<=0?(r&&(s(r),r=null),Promise.resolve(u())):new Promise((f,v)=>{o=n.rejectOnCancel?v:f,d&&!r&&(r=setTimeout(()=>{t&&s(t),r=null,f(u())},d)),t=setTimeout(()=>{r&&s(r),r=null,f(u())},l)})}}function de(e,n=!0,t=!0,r=!1){let o=0,s,i=!0,u=b,l;const d=()=>{s&&(clearTimeout(s),s=void 0,u(),u=b)};return v=>{const a=_(e),c=Date.now()-o,p=()=>l=v();return d(),a<=0?(o=Date.now(),p()):(c>a&&(t||!i)?(o=Date.now(),p()):n&&(l=new Promise((w,h)=>{u=r?h:w,s=setTimeout(()=>{o=Date.now(),i=!0,w(p()),d()},Math.max(0,a-c))})),!t&&!s&&(s=setTimeout(()=>i=!0,a)),i=!1,l)}}function pe(e){return e}function E(e){return G()?(q(e),!0):!1}function ve(e,n=200,t={}){return B(fe(n,t),e)}function Ce(e,n=200,t={}){const r=g(e.value),o=ve(()=>{r.value=e.value},n,t);return O(e,()=>o()),r}function Ne(e,n=200,t=!1,r=!0,o=!1){return B(de(n,t,r,o),e)}function me(e,n=!0){H()?k(e):n?e():X(e)}function Fe(e,n,t={}){const{immediate:r=!0}=t,o=g(!1);let s=null;function i(){s&&(clearTimeout(s),s=null)}function u(){o.value=!1,i()}function l(...d){i(),o.value=!0,s=setTimeout(()=>{o.value=!1,s=null,e(...d)},_(n))}return r&&(o.value=!0,T&&l()),E(u),{isPending:W(o),start:l,stop:u}}function m(e){var n;const t=_(e);return(n=t==null?void 0:t.$el)!=null?n:t}const P=T?window:void 0;function $(...e){let n,t,r,o;if(le(e[0])||Array.isArray(e[0])?([t,r,o]=e,n=P):[n,t,r,o]=e,!n)return b;Array.isArray(t)||(t=[t]),Array.isArray(r)||(r=[r]);const s=[],i=()=>{s.forEach(f=>f()),s.length=0},u=(f,v,a,c)=>(f.addEventListener(v,a,c),()=>f.removeEventListener(v,a,c)),l=O(()=>[m(n),_(o)],([f,v])=>{i(),f&&s.push(...t.flatMap(a=>r.map(c=>u(f,a,c,v))))},{immediate:!0,flush:"post"}),d=()=>{l(),i()};return E(d),d}let C=!1;function Re(e,n,t={}){const{window:r=P,ignore:o=[],capture:s=!0,detectIframe:i=!1}=t;if(!r)return;ce&&!C&&(C=!0,Array.from(r.document.body.children).forEach(a=>a.addEventListener("click",b)));let u=!0;const l=a=>o.some(c=>{if(typeof c=="string")return Array.from(r.document.querySelectorAll(c)).some(p=>p===a.target||a.composedPath().includes(p));{const p=m(c);return p&&(a.target===p||a.composedPath().includes(p))}}),f=[$(r,"click",a=>{const c=m(e);if(!(!c||c===a.target||a.composedPath().includes(c))){if(a.detail===0&&(u=!l(a)),!u){u=!0;return}n(a)}},{passive:!0,capture:s}),$(r,"pointerdown",a=>{const c=m(e);c&&(u=!a.composedPath().includes(c)&&!l(a))},{passive:!0}),i&&$(r,"blur",a=>{var c;const p=m(e);((c=r.document.activeElement)==null?void 0:c.tagName)==="IFRAME"&&!(p!=null&&p.contains(r.document.activeElement))&&n(a)})].filter(Boolean);return()=>f.forEach(a=>a())}function z(e,n=!1){const t=g(),r=()=>t.value=!!e();return r(),me(r,n),t}function Oe(e){return JSON.parse(JSON.stringify(e))}const N=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},F="__vueuse_ssr_handlers__";N[F]=N[F]||{};function De(e,n,{window:t=P,initialValue:r=""}={}){const o=g(r),s=L(()=>{var i;return m(n)||((i=t==null?void 0:t.document)==null?void 0:i.documentElement)});return O([s,()=>_(e)],([i,u])=>{var l;if(i&&t){const d=(l=t.getComputedStyle(i).getPropertyValue(u))==null?void 0:l.trim();o.value=d||r}},{immediate:!0}),O(o,i=>{var u;(u=s.value)!=null&&u.style&&s.value.style.setProperty(_(e),i)}),o}var R=Object.getOwnPropertySymbols,_e=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable,be=(e,n)=>{var t={};for(var r in e)_e.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&R)for(var r of R(e))n.indexOf(r)<0&&ye.call(e,r)&&(t[r]=e[r]);return t};function Me(e,n,t={}){const r=t,{window:o=P}=r,s=be(r,["window"]);let i;const u=z(()=>o&&"ResizeObserver"in o),l=()=>{i&&(i.disconnect(),i=void 0)},d=O(()=>m(e),v=>{l(),u.value&&o&&v&&(i=new ResizeObserver(n),i.observe(v,s))},{immediate:!0,flush:"post"}),f=()=>{l(),d()};return E(f),{isSupported:u,stop:f}}var D=Object.getOwnPropertySymbols,we=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable,Pe=(e,n)=>{var t={};for(var r in e)we.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&D)for(var r of D(e))n.indexOf(r)<0&&ge.call(e,r)&&(t[r]=e[r]);return t};function Qe(e,n,t={}){const r=t,{window:o=P}=r,s=Pe(r,["window"]);let i;const u=z(()=>o&&"MutationObserver"in o),l=()=>{i&&(i.disconnect(),i=void 0)},d=O(()=>m(e),v=>{l(),u.value&&o&&v&&(i=new MutationObserver(n),i.observe(v,s))},{immediate:!0}),f=()=>{l(),d()};return E(f),{isSupported:u,stop:f}}var M;(function(e){e.UP="UP",e.RIGHT="RIGHT",e.DOWN="DOWN",e.LEFT="LEFT",e.NONE="NONE"})(M||(M={}));var he=Object.defineProperty,Q=Object.getOwnPropertySymbols,Ie=Object.prototype.hasOwnProperty,Ee=Object.prototype.propertyIsEnumerable,V=(e,n,t)=>n in e?he(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Se=(e,n)=>{for(var t in n||(n={}))Ie.call(n,t)&&V(e,t,n[t]);if(Q)for(var t of Q(n))Ee.call(n,t)&&V(e,t,n[t]);return e};const $e={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};Se({linear:pe},$e);function Ve(e,n,t,r={}){var o,s,i;const{clone:u=!1,passive:l=!1,eventName:d,deep:f=!1,defaultValue:v}=r,a=H(),c=(a==null?void 0:a.emit)||((o=a==null?void 0:a.$emit)==null?void 0:o.bind(a))||((i=(s=a==null?void 0:a.proxy)==null?void 0:s.$emit)==null?void 0:i.bind(a==null?void 0:a.proxy));let p=d;p=d||p||`update:${n.toString()}`;const w=y=>u?ue(u)?u(y):Oe(y):y,h=()=>ie(e[n])?w(e[n]):v;if(l){const y=h(),S=g(y);return O(()=>e[n],I=>S.value=w(I)),O(S,I=>{(I!==e[n]||f)&&c(p,I)},{deep:f}),S}else return L({get(){return h()},set(y){c(p,y)}})}export{Me as a,m as b,Ae as c,De as d,Fe as e,Ne as f,ce as g,Qe as h,T as i,Ve as j,xe as k,Re as o,Ce as r,E as t,$ as u};
