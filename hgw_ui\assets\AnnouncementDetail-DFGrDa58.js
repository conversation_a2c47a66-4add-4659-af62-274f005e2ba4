import{b as T,E as h,v as x}from"./element-plus-DZBbDeaO.js";import{S as B,_ as I}from"./index-CkIekciI.js";import{a as M,u as q}from"./vue-router-e9iWfNxP.js";import{b as C,o as E}from"./utils-common-CvYGMv_l.js";import{d as $,r as l,c as y,s as L,t as R,a as d,b as a,O as V,K as u,I as b,G,$ as H,o as m}from"./vue-vendor-2E6AJATX.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./app-stores-DSLz6__G.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";const F={class:"announcement-detail-container"},K={class:"breadcrumb"},O={class:"current"},P={key:0,class:"announcement-content"},Y={class:"announcement-title"},j={class:"announcement-meta"},z={class:"publish-time"},J=["innerHTML"],Q={key:1,class:"loading-container","element-loading-text":"加载中..."},U={key:2,class:"error-container"},W={class:"error-content"},X=$({__name:"AnnouncementDetail",setup(Z){const s=M(),v=q(),o=l(null),k=l([]),i=l(!0),c=l(!1),r=y(()=>s.query.id||s.params.id),S=y(()=>s.query.crumbsTitle||"公告信息"),f=async()=>{if(!r.value){c.value=!0,i.value=!1;return}try{i.value=!0,c.value=!1;const n=s.query.type;let e;if(n==="auction"){if(e=await C.getAuctionSessionAnnouncement({id:r.value}),e.code===1&&e.data){const t=e.data;o.value={id:Number(r.value),title:t.pmh_name||"",content:t.pmh_gonggao||"",addtime:t.addtime?A(t.addtime):"",cate_name:"拍卖会公告"}}}else if(e=await E.getTenderDetail({id:r.value}),e.code===1&&e.data){const t=e.data;o.value={id:Number(r.value),title:t.title||"",content:t.content||"",addtime:t.addtime||"",cate_name:n==="purchase"?"采购公告":"销售公告"}}o.value?await w():(c.value=!0,h.error("公告不存在或已被删除"))}catch(n){console.error("获取公告详情失败:",n),c.value=!0,h.error("获取公告详情失败，请稍后重试")}finally{i.value=!1}},A=n=>{const e=new Date(Number(n)),t=e.getFullYear(),p=String(e.getMonth()+1).padStart(2,"0"),_=String(e.getDate()).padStart(2,"0");return`${t}-${p}-${_}`},w=async()=>{try{k.value=[{id:2,title:"关于调整拍卖保证金比例的通知",addtime:"2024-03-10 14:20:00"},{id:3,title:"2024年春季拍卖会征集公告",addtime:"2024-03-05 09:15:00"},{id:4,title:"拍卖规则更新说明",addtime:"2024-02-28 16:45:00"}]}catch(n){console.error("获取相关公告失败:",n)}},D=()=>{v.back()},N=()=>{v.go(-1)};return L(()=>{f()}),R(()=>s.query.id,n=>{n&&f()}),(n,e)=>{var g;const t=B,p=T,_=x;return m(),d("div",F,[a("div",K,[a("span",{class:"breadcrumb-title",onClick:D},u(S.value),1),e[0]||(e[0]=a("span",{class:"separator"},">",-1)),a("span",O,u(((g=o.value)==null?void 0:g.title)||"公告详情"),1)]),o.value?(m(),d("div",P,[a("span",Y,u(o.value.title),1),a("div",j,[a("span",z,"发布时间："+u(o.value.addtime),1)]),a("div",{class:"announcement-body",innerHTML:o.value.content},null,8,J)])):i.value?V((m(),d("div",Q,null,512)),[[_,!0]]):(m(),d("div",U,[a("div",W,[b(t,{iconName:"warning",class:"error-icon"}),e[2]||(e[2]=a("h3",null,"公告不存在或已被删除",-1)),e[3]||(e[3]=a("p",null,"您访问的公告可能已被删除或不存在，请检查链接是否正确。",-1)),b(p,{type:"primary",onClick:N},{default:G(()=>e[1]||(e[1]=[H("返回上一页")])),_:1,__:[1]})])]))])}}}),fe=I(X,[["__scopeId","data-v-a50f12d6"]]);export{fe as default};
