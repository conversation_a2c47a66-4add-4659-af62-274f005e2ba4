import{u as V,b as S}from"./element-plus-DZBbDeaO.js";import{S as p,_ as g}from"./index-CkIekciI.js";import{d as N,r as z,t as f,E as s,ab as T,G as a,b as t,n as y,C as d,K as c,I,o as n,$ as B}from"./vue-vendor-2E6AJATX.js";const O={class:"modal-header"},E={class:"modal-title"},M={class:"title-text"},_={class:"modal-content"},$={class:"modal-footer"},D=N({__name:"Modal",props:{modelValue:{type:Boolean},title:{default:"提示"},titleIcon:{},width:{default:"500px"},top:{default:"15vh"},modal:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},destroyOnClose:{type:Boolean,default:!1},appendToBody:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},customClass:{default:""},showFooter:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},cancelButtonText:{default:"取消"},confirmButtonText:{default:"确定"},confirmLoading:{type:Boolean,default:!1},buttonSize:{default:"default"}},emits:["update:modelValue","confirm","cancel","close","open","opened","closed"],setup(h,{emit:C}){const i=h,o=C,l=z(i.modelValue);f(()=>i.modelValue,e=>{l.value=e,e&&o("open")},{immediate:!0}),f(l,e=>{o("update:modelValue",e),o(e?"opened":"closed")});const u=()=>{l.value=!1,o("close")},k=()=>{o("confirm")},v=()=>{l.value=!1,o("cancel")};return(e,r)=>{const m=S,b=V;return n(),s(b,{modelValue:l.value,"onUpdate:modelValue":r[0]||(r[0]=w=>l.value=w),title:e.title,width:e.width,top:e.top,modal:e.modal,"close-on-click-modal":e.closeOnClickModal,"close-on-press-escape":e.closeOnPressEscape,"show-close":!1,"before-close":u,"destroy-on-close":e.destroyOnClose,"append-to-body":e.appendToBody,"lock-scroll":e.lockScroll,"custom-class":e.customClass,class:"custom-modal"},T({header:a(()=>[t("div",O,[t("div",E,[e.titleIcon?(n(),s(p,{key:0,iconName:e.titleIcon,className:"title-icon"},null,8,["iconName"])):d("",!0)]),t("span",M,c(e.title),1),t("div",{class:"modal-close",onClick:u},[I(p,{iconName:"model-close",className:"close-icon"})])])]),default:a(()=>[t("div",_,[y(e.$slots,"default",{},void 0,!0)])]),_:2},[e.showFooter?{name:"footer",fn:a(()=>[t("div",$,[y(e.$slots,"footer",{},()=>[e.showCancelButton?(n(),s(m,{key:0,onClick:v,size:e.buttonSize,class:"cancel-btn"},{default:a(()=>[B(c(e.cancelButtonText),1)]),_:1},8,["size"])):d("",!0),e.showConfirmButton?(n(),s(m,{key:1,type:"primary",onClick:k,loading:e.confirmLoading,size:e.buttonSize},{default:a(()=>[B(c(e.confirmButtonText),1)]),_:1},8,["loading","size"])):d("",!0)],!0)])]),key:"0"}:void 0]),1032,["modelValue","title","width","top","modal","close-on-click-modal","close-on-press-escape","destroy-on-close","append-to-body","lock-scroll","custom-class"])}}}),G=g(D,[["__scopeId","data-v-fcbb7871"]]);export{G as M};
