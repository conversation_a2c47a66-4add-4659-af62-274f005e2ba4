import{l as B}from"./element-plus-DZBbDeaO.js";import{u as P}from"./vue-router-e9iWfNxP.js";import{a as j,_ as z}from"./index-CkIekciI.js";import{o as C,b as D}from"./utils-common-CvYGMv_l.js";import{d as E,r as c,c as f,s as $,a as r,b as u,F as v,a1 as k,E as F,C as b,D as q,K,I as M,o as s}from"./vue-vendor-2E6AJATX.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./app-stores-DSLz6__G.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";const R={class:"announcement-list-page"},G={class:"tab-nav"},H=["onClick"],J={class:"list-container"},O={class:"auction-list"},Q={key:0,class:"item-divider"},l=10,U=E({__name:"AnnouncementList",setup(W){const w=P(),A=[{label:"采购公告",value:"purchase"},{label:"销售公告",value:"sale"},{label:"拍卖会公告",value:"auction"}],i=c("purchase"),p=c([]),g=c(!1),m=c(1),o=c(0),h={purchase:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/purchase_1754962265712.png",sale:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/sale_1754962281689.png"},L=async(a=1)=>{try{const e=await C.getTenderList({cateid:"29",limit:l,page:a});return e.code===1&&e.data?(o.value=e.data.total,e.data.data.map(t=>({id:t.id.toString(),type:"purchase",title:t.title,image:h.purchase,description:t.title,companyName:t.cate_name||"采购公告",timeValue:t.addtime,likeCount:0,commentCount:0,viewCount:0}))):[]}catch(e){return console.error("获取采购公告失败:",e),[]}},N=async(a=1)=>{try{const e=await C.getTenderList({cateid:"30",limit:l,page:a});return e.code===1&&e.data?(o.value=e.data.total,e.data.data.map(t=>({id:t.id.toString(),type:"sale",title:t.title,image:h.sale,description:t.title,companyName:t.cate_name||"销售公告",timeValue:t.addtime,likeCount:0,commentCount:0,viewCount:0}))):[]}catch(e){return console.error("获取销售公告失败:",e),[]}},I=async(a=1)=>{try{const e=await D.getAuctionSessionAnnouncementList({page:a});return e.code===1&&e.data?(o.value=e.data.total,e.data.data.map(t=>({id:t.id.toString(),type:"auction",title:t.pmh_name,image:t.pmh_pic?`https://huigupaimai.oss-cn-beijing.aliyuncs.com/${t.pmh_pic}`:"",description:t.pmh_gonggao||t.pmh_name,companyName:t.parentname||"拍卖会公告",timeValue:t.start_time_name||t.addtime,likeCount:0,commentCount:0,viewCount:0}))):[]}catch(e){return console.error("获取拍卖会公告失败:",e),[]}},d=async(a=1)=>{g.value=!0;try{let e=[];switch(i.value){case"purchase":e=await L(a);break;case"sale":e=await N(a);break;case"auction":e=await I(a);break}p.value=e}catch(e){console.error("加载数据失败:",e),p.value=[],o.value=0}finally{g.value=!1}},V=f(()=>p.value.filter(a=>a.type===i.value)),_=f(()=>V.value);function S(a){i.value=a,m.value=1,d(1)}function T(a){m.value=a,d(a)}function x(a){w.push({name:"announcementInfo-detail",query:{id:a,type:i.value,crumbsTitle:"公告信息"}})}return $(()=>{d(1)}),(a,e)=>{const t=B;return s(),r("div",R,[u("div",G,[(s(),r(v,null,k(A,n=>u("div",{key:n.value,class:q(["tab-item",{active:i.value===n.value}]),onClick:y=>S(n.value)},K(n.label),11,H)),64))]),u("div",J,[u("div",O,[(s(!0),r(v,null,k(_.value,(n,y)=>(s(),r(v,{key:n.id},[M(j,{productId:n.id,productName:n.title,productImage:n.image||"",description:n.description,companyName:n.companyName,timeValue:n.timeValue,likeCount:n.likeCount,commentCount:n.commentCount,viewCount:n.viewCount,onClick:()=>x(n.id)},null,8,["productId","productName","productImage","description","companyName","timeValue","likeCount","commentCount","viewCount","onClick"]),y<_.value.length-1?(s(),r("div",Q)):b("",!0)],64))),128))]),o.value>l?(s(),F(t,{key:0,class:"pagination",background:"",layout:"prev, pager, next",total:o.value,"page-size":l,"current-page":m.value,onCurrentChange:T},null,8,["total","current-page"])):b("",!0)])])}}}),de=z(U,[["__scopeId","data-v-f4b60768"]]);export{de as default};
