import{u as e}from"./vue-router-C0lzQS1p.js";import{_ as s,S as a}from"./index-CCmno-0X.js";import{d as r,e as l,f as o,g as t,b as n,E as i}from"./element-plus-BiAL0NdQ.js";import{d,r as u,Q as m,E as c,G as p,I as g,b as w,$ as v,J as f,o as h,K as b,u as _,D as y,a as V,F as C,c as S,ax as k,C as T,H as x}from"./vue-vendor-D6tHD5lA.js";import{g as P,c as z,s as U,r as j}from"./utils-common-PdkFOSu3.js";import{u as I}from"./app-stores-CLUCXxRF.js";import{M as q}from"./Modal-DCQzYcok.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";import"./element-icons-Cfq32zG_.js";import"./vendor-lodash-D0OfQ6x6.js";const $={class:"forgot-password-link"},F={class:"register-link"},B=s(d({__name:"PasswordLogin",emits:["switchTab","loginSuccess"],setup(e,{emit:s}){const a=s,d=I(),b=u(!1),_=u(),y=m({userType:"",username:"",password:"",rememberMe:!1}),V={userType:[{required:!0,message:"请选择登录身份",trigger:"change"}],username:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},C=async()=>{if(_.value)try{await _.value.validate(),b.value=!0;const e=await z.login({mobile:y.username,password:y.password});if(1===e.code){const s=e.data;d.login(s),y.rememberMe?(localStorage.setItem("rememberedUsername",y.username),U("rememberedPassword",y.password),localStorage.setItem("rememberedUserType",y.userType)):(localStorage.removeItem("rememberedUsername"),j("rememberedPassword"),localStorage.removeItem("rememberedUserType")),i.success("登录成功"),a("loginSuccess")}else i.error(e.msg||"登录失败，请检查用户名和密码")}catch(e){i.error("登录失败，请检查用户名和密码")}finally{b.value=!1}},S=()=>{a("switchTab","forgot")},k=()=>{a("switchTab","register")},T=localStorage.getItem("rememberedUsername"),x=P("rememberedPassword"),q=localStorage.getItem("rememberedUserType");return T&&x&&q&&(y.username=T,y.password=x,y.userType=q,y.rememberMe=!0),(e,s)=>{const a=l,i=o,d=t,u=n,m=r;return h(),c(m,{ref_key:"passwordFormRef",ref:_,model:y,rules:V,"validate-on-rule-change":!1,class:"login-form"},{default:p(()=>[g(i,{prop:"username"},{default:p(()=>[g(a,{modelValue:y.username,"onUpdate:modelValue":s[0]||(s[0]=e=>y.username=e),placeholder:"请输入手机号",size:"large",class:"login-input"},null,8,["modelValue"])]),_:1}),g(i,{prop:"password",class:"password"},{default:p(()=>[g(a,{modelValue:y.password,"onUpdate:modelValue":s[1]||(s[1]=e=>y.password=e),type:"password",placeholder:"请输入登录密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),g(i,{class:"form-options"},{default:p(()=>[g(d,{modelValue:y.rememberMe,"onUpdate:modelValue":s[2]||(s[2]=e=>y.rememberMe=e),class:"remember-checkbox"},{default:p(()=>s[3]||(s[3]=[v(" 记住密码 ")])),_:1,__:[3]},8,["modelValue"]),w("div",$,[w("a",{href:"#",onClick:f(S,["prevent"])},"忘记密码")])]),_:1}),g(i,null,{default:p(()=>[g(u,{type:"primary",size:"large",class:"login-button",loading:b.value,onClick:C},{default:p(()=>s[4]||(s[4]=[v(" 登录 ")])),_:1,__:[4]},8,["loading"])]),_:1}),w("div",F,[s[5]||(s[5]=w("span",null,"还没有账号？",-1)),w("a",{href:"#",onClick:f(k,["prevent"])},"立即注册")])]),_:1},8,["model"])}}}),[["__scopeId","data-v-ae60c0a4"]]);function E(){const e=u(0);let s=null;const a=(a=60)=>{s&&clearInterval(s),e.value=a,s=setInterval(()=>{e.value--,e.value<=0&&(clearInterval(s),s=null)},1e3)};return{smsCountdown:e,sendSms:async s=>{if(!s)return i.warning("请先输入手机号"),!1;if(!/^1[3-9]\d{9}$/.test(s))return i.warning("请输入正确的手机号"),!1;if(e.value>0)return i.warning("请等待倒计时结束后再发送"),!1;try{const e=await z.sendSms({mobile:s});return 1===e.code?(a(),i.success("验证码已发送"),!0):(i.error(e.msg||"发送验证码失败"),!1)}catch(r){return i.error("发送验证码失败，请重试"),!1}},startCountdown:a,clearCountdown:()=>{s&&(clearInterval(s),s=null),e.value=0},getSmsButtonText:()=>e.value>0?`${e.value}s后重发`:"获取验证码",isSmsButtonDisabled:()=>e.value>0}}const M={class:"register-link"},R=s(d({__name:"SmsLogin",emits:["switchTab","loginSuccess"],setup(e,{emit:s}){const a=s,t=I(),{sendSms:d,getSmsButtonText:V,isSmsButtonDisabled:C}=E(),S=u(!1),k=u(),T=m({userType:"",phone:"",smsCode:""}),x={userType:[{required:!0,message:"请选择登录身份",trigger:"change"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{4}$/,message:"验证码为4位数字",trigger:"blur"}]},P=async()=>{if(k.value)try{await k.value.validateField("phone"),await d(T.phone)}catch(e){}},U=async()=>{if(k.value)try{await k.value.validate(),S.value=!0;const e=await z.smsLogin({mobile:T.phone,yzcode:T.smsCode});if(1===e.code){const s=e.data;t.login(s),i.success("登录成功"),a("loginSuccess")}else i.error(e.msg||"登录失败，请检查手机号和验证码")}catch(e){i.error("登录失败，请检查手机号和验证码")}finally{S.value=!1}},j=()=>{a("switchTab","register")};return(e,s)=>{const a=l,t=o,i=n,d=r;return h(),c(d,{ref_key:"smsFormRef",ref:k,model:T,rules:x,"validate-on-rule-change":!1,class:"login-form"},{default:p(()=>[g(t,{prop:"phone"},{default:p(()=>[g(a,{modelValue:T.phone,"onUpdate:modelValue":s[0]||(s[0]=e=>T.phone=e),placeholder:"请输入手机号",size:"large",class:"login-input",maxlength:"11"},null,8,["modelValue"])]),_:1}),g(t,{prop:"smsCode",class:"password"},{default:p(()=>[g(a,{modelValue:T.smsCode,"onUpdate:modelValue":s[1]||(s[1]=e=>T.smsCode=e),placeholder:"请输入验证码",size:"large",class:"login-input sms-input-with-button",maxlength:"6"},{suffix:p(()=>[w("span",{class:y(["sms-text-button",{disabled:_(C)()}]),onClick:P},b(_(V)()),3)]),_:1},8,["modelValue"])]),_:1}),g(t,{class:"form-options"},{default:p(()=>s[2]||(s[2]=[w("div",{class:"sms-tips"},[w("span",null,"验证即登录，未注册将自动创建账号")],-1)])),_:1,__:[2]}),g(t,null,{default:p(()=>[g(i,{type:"primary",size:"large",class:"login-button",loading:S.value,onClick:U},{default:p(()=>s[3]||(s[3]=[v(" 登录 ")])),_:1,__:[3]},8,["loading"])]),_:1}),w("div",M,[s[4]||(s[4]=w("span",null,"还没有账号？",-1)),w("a",{href:"#",onClick:f(j,["prevent"])},"立即注册")])]),_:1},8,["model"])}}}),[["__scopeId","data-v-c918e4a6"]]),D={class:"login-link"},A=s(d({__name:"Register",emits:["switchTab","registerSuccess"],setup(e,{emit:s}){const a=s,{sendSms:d,getSmsButtonText:c,isSmsButtonDisabled:S}=E(),k=u(!1),T=u(!1),x=u(!1),P=u(),U=m({phone:"",smsCode:"",password:"",confirmPassword:"",agreeTerms:!1}),j={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{4}$/,message:"验证码为4位数字",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度为6-20位",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,validator:(e,s,a)=>{""===s?a(new Error("请再次输入密码")):s!==U.password?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}],agreeTerms:[{required:!0,validator:(e,s,a)=>{s?a():a(new Error("请先同意用户协议和隐私政策"))},trigger:"change"}]},I=async()=>{if(P.value)try{await P.value.validateField("phone"),await d(U.phone)}catch(e){}},$=async()=>{if(P.value)try{await P.value.validate(),k.value=!0;const e=await z.register({mobile:U.phone,password:U.password,yzcode:U.smsCode});1===e.code?(i.success("注册成功，请登录"),a("registerSuccess"),a("switchTab","password")):i.error(e.msg||"注册失败，请重试")}catch(e){i.error("注册失败，请重试")}finally{k.value=!1}},F=()=>{T.value=!0},B=()=>{x.value=!0},M=()=>{a("switchTab","password")};return(e,s)=>{const a=l,i=o,d=t,u=n,m=r;return h(),V(C,null,[g(m,{ref_key:"registerFormRef",ref:P,model:U,rules:j,"validate-on-rule-change":!1,class:"login-form"},{default:p(()=>[g(i,{prop:"phone"},{default:p(()=>[g(a,{modelValue:U.phone,"onUpdate:modelValue":s[0]||(s[0]=e=>U.phone=e),placeholder:"请输入手机号",size:"large",class:"login-input",maxlength:"11"},null,8,["modelValue"])]),_:1}),g(i,{prop:"smsCode"},{default:p(()=>[g(a,{modelValue:U.smsCode,"onUpdate:modelValue":s[1]||(s[1]=e=>U.smsCode=e),placeholder:"请输入验证码",size:"large",class:"login-input sms-input-with-button",maxlength:"6"},{suffix:p(()=>[w("span",{class:y(["sms-text-button",{disabled:_(S)()}]),onClick:I},b(_(c)()),3)]),_:1},8,["modelValue"])]),_:1}),g(i,{prop:"password"},{default:p(()=>[g(a,{modelValue:U.password,"onUpdate:modelValue":s[2]||(s[2]=e=>U.password=e),type:"password",placeholder:"请输入密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),g(i,{prop:"confirmPassword"},{default:p(()=>[g(a,{modelValue:U.confirmPassword,"onUpdate:modelValue":s[3]||(s[3]=e=>U.confirmPassword=e),type:"password",placeholder:"请再次输入密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),g(i,{prop:"agreeTerms",class:"agreement-item"},{default:p(()=>[g(d,{modelValue:U.agreeTerms,"onUpdate:modelValue":s[4]||(s[4]=e=>U.agreeTerms=e),class:"agreement-checkbox"},{default:p(()=>[s[7]||(s[7]=v(" 我已阅读并同意 ")),w("a",{href:"#",class:"agreement-link",onClick:f(F,["prevent"])}," 《平台服务协议》 "),s[8]||(s[8]=v(" 和 ")),w("a",{href:"#",class:"agreement-link",onClick:f(B,["prevent"])}," 《隐私政策》 ")]),_:1,__:[7,8]},8,["modelValue"])]),_:1}),g(i,null,{default:p(()=>[g(u,{type:"primary",size:"large",class:"login-button",loading:k.value,onClick:$},{default:p(()=>s[9]||(s[9]=[v(" 立即注册 ")])),_:1,__:[9]},8,["loading"])]),_:1}),w("div",D,[s[10]||(s[10]=w("span",null,"已有账号？",-1)),w("a",{href:"#",onClick:f(M,["prevent"])},"立即登录")])]),_:1},8,["model"]),g(q,{modelValue:T.value,"onUpdate:modelValue":s[5]||(s[5]=e=>T.value=e),title:"平台服务协议",width:"600px","show-footer":!1},{default:p(()=>s[11]||(s[11]=[w("div",{class:"agreement-content"},[w("p",null,"这里是平台服务协议的内容，后续会添加具体条款...")],-1)])),_:1,__:[11]},8,["modelValue"]),g(q,{modelValue:x.value,"onUpdate:modelValue":s[6]||(s[6]=e=>x.value=e),title:"隐私政策",width:"600px","show-footer":!1},{default:p(()=>s[12]||(s[12]=[w("div",{class:"agreement-content"},[w("p",null,"这里是隐私政策的内容，后续会添加具体条款...")],-1)])),_:1,__:[12]},8,["modelValue"])],64)}}}),[["__scopeId","data-v-6e8381c6"]]),L={class:"login-link"},Z=s(d({__name:"ForgotPassword",emits:["switchTab","resetSuccess"],setup(e,{emit:s}){const a=s,{sendSms:t,getSmsButtonText:d,isSmsButtonDisabled:V}=E(),C=u(!1),S=u(),k=m({phone:"",smsCode:"",newPassword:"",confirmPassword:""}),T={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{4}$/,message:"验证码为4位数字",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,max:20,message:"密码长度为6-20位",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,validator:(e,s,a)=>{""===s?a(new Error("请再次输入新密码")):s!==k.newPassword?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}]},x=async()=>{if(S.value)try{await S.value.validateField("phone"),await t(k.phone)}catch(e){}},P=async()=>{if(S.value)try{await S.value.validate(),C.value=!0;const e=await z.resetPassword({mobile:k.phone,yzcode:k.smsCode,password:k.newPassword});1===e.code?(i.success("密码重置成功，请使用新密码登录"),a("resetSuccess"),a("switchTab","password"),j()):i.error(e.msg||"密码重置失败，请重试")}catch(e){i.error("密码重置失败，请重试")}finally{C.value=!1}},U=()=>{a("switchTab","password")},j=()=>{S.value&&S.value.resetFields(),Object.assign(k,{phone:"",smsCode:"",newPassword:"",confirmPassword:""})};return(e,s)=>{const a=l,t=o,i=n,u=r;return h(),c(u,{ref_key:"forgotFormRef",ref:S,model:k,rules:T,"validate-on-rule-change":!1,class:"login-form"},{default:p(()=>[g(t,{prop:"phone"},{default:p(()=>[g(a,{modelValue:k.phone,"onUpdate:modelValue":s[0]||(s[0]=e=>k.phone=e),placeholder:"请输入手机号",size:"large",class:"login-input",maxlength:"11"},null,8,["modelValue"])]),_:1}),g(t,{prop:"smsCode",class:"password"},{default:p(()=>[g(a,{modelValue:k.smsCode,"onUpdate:modelValue":s[1]||(s[1]=e=>k.smsCode=e),placeholder:"请输入验证码",size:"large",class:"login-input sms-input-with-button",maxlength:"6"},{suffix:p(()=>[w("span",{class:y(["sms-text-button",{disabled:_(V)()}]),onClick:x},b(_(d)()),3)]),_:1},8,["modelValue"])]),_:1}),g(t,{prop:"newPassword"},{default:p(()=>[g(a,{modelValue:k.newPassword,"onUpdate:modelValue":s[2]||(s[2]=e=>k.newPassword=e),type:"password",placeholder:"请输入新密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),g(t,{prop:"confirmPassword"},{default:p(()=>[g(a,{modelValue:k.confirmPassword,"onUpdate:modelValue":s[3]||(s[3]=e=>k.confirmPassword=e),type:"password",placeholder:"请再次输入新密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),g(t,null,{default:p(()=>[g(i,{type:"primary",size:"large",class:"login-button",loading:C.value,onClick:P},{default:p(()=>s[4]||(s[4]=[v(" 重置密码 ")])),_:1,__:[4]},8,["loading"])]),_:1}),w("div",L,[s[5]||(s[5]=w("span",null,"想起密码了？",-1)),w("a",{href:"#",onClick:f(U,["prevent"])},"返回登录")])]),_:1},8,["model"])}}}),[["__scopeId","data-v-5b2f41e1"]]),N={class:"login-container"},G={class:"login-right"},H={class:"login-form-container"},J={class:"form-logo"},K={class:"welcome-title"},O={key:0,class:"tab-container"},Q=s(d({__name:"index",setup(s){const r=e(),l=u("password"),o={password:B,sms:R,register:A,forgot:Z},t=S(()=>o[l.value]),n=S(()=>["password","sms"].includes(l.value)),i=e=>{l.value=e},d=()=>{r.push("/")},m=()=>{i("password")},p=()=>{i("password")};return(e,s)=>(h(),V("div",N,[s[2]||(s[2]=k('<div class="login-left" data-v-5165fe24><div class="logo-3d-wrapper" data-v-5165fe24><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1_1754963030315.png" alt="" class="logo-3d-image-1" data-v-5165fe24><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/2_1754963119472.png" alt="" class="logo-3d-image-2" data-v-5165fe24><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/3_1754963135630.png" alt="" class="logo-3d-image-3" data-v-5165fe24></div></div>',1)),w("div",G,[w("div",H,[w("div",J,[g(a,{iconName:"login-logo",className:"logo-icon"})]),w("h2",K,b({password:"欢迎登陆",sms:"欢迎登陆",register:"用户注册",forgot:"重置密码"}[l.value]||"欢迎登陆"),1),n.value?(h(),V("div",O,[w("div",{class:y(["tab-item",{active:"password"===l.value}]),onClick:s[0]||(s[0]=e=>i("password"))}," 账号密码登录 ",2),w("div",{class:y(["tab-item",{active:"sms"===l.value}]),onClick:s[1]||(s[1]=e=>i("sms"))}," 验证码登录 ",2)])):T("",!0),(h(),c(x(t.value),{onSwitchTab:i,onLoginSuccess:d,onRegisterSuccess:m,onResetSuccess:p},null,32))])])]))}}),[["__scopeId","data-v-5165fe24"]]);export{Q as default};
