var h=Object.defineProperty;var f=(i,e,t)=>e in i?h(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var n=(i,e,t)=>f(i,typeof e!="symbol"?e+"":e,t);const o={FCP_THRESHOLD:1800,LCP_THRESHOLD:2500,FID_THRESHOLD:100,CLS_THRESHOLD:.1,RESOURCE_THRESHOLD:3e3};class m{constructor(){n(this,"metrics",{});n(this,"observers",[]);n(this,"startTime");this.startTime=performance.now(),this.initObservers(),this.monitorPageLoad()}initObservers(){"PerformanceObserver"in window&&(this.observePaintMetrics(),this.observeLayoutShift(),this.observeResourceTiming())}observePaintMetrics(){try{const e=new PerformanceObserver(t=>{for(const r of t.getEntries())r.name==="first-contentful-paint"&&(this.metrics.firstContentfulPaint=r.startTime,this.checkThreshold("FCP",r.startTime,o.FCP_THRESHOLD))});e.observe({entryTypes:["paint"]}),this.observers.push(e)}catch{}try{const e=new PerformanceObserver(t=>{const r=t.getEntries(),s=r[r.length-1];this.metrics.largestContentfulPaint=s.startTime,this.checkThreshold("LCP",s.startTime,o.LCP_THRESHOLD)});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}catch{}}observeLayoutShift(){try{let e=0;const t=new PerformanceObserver(r=>{for(const s of r.getEntries())s.hadRecentInput||(e+=s.value);this.metrics.cumulativeLayoutShift=e,this.checkThreshold("CLS",e,o.CLS_THRESHOLD)});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}catch{}}observeResourceTiming(){try{const e=new PerformanceObserver(t=>{const r=[];for(const s of t.getEntries()){const a={name:s.name,type:this.getResourceType(s.name),startTime:s.startTime,duration:s.duration,size:s.transferSize||0};r.push(a),s.duration>o.RESOURCE_THRESHOLD}this.metrics.resourceLoadTimes=[...this.metrics.resourceLoadTimes||[],...r]});e.observe({entryTypes:["resource"]}),this.observers.push(e)}catch{}}monitorPageLoad(){document.addEventListener("DOMContentLoaded",()=>{this.metrics.domContentLoaded=performance.now()-this.startTime}),window.addEventListener("load",()=>{this.metrics.loadComplete=performance.now()-this.startTime,setTimeout(()=>{this.generatePerformanceReport()},1e3)}),this.monitorFirstInputDelay()}monitorFirstInputDelay(){try{const e=new PerformanceObserver(t=>{for(const r of t.getEntries())this.metrics.firstInputDelay=r.processingStart-r.startTime,this.checkThreshold("FID",this.metrics.firstInputDelay,o.FID_THRESHOLD)});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}catch{}}getResourceType(e){return e.includes(".js")?"script":e.includes(".css")?"stylesheet":e.match(/\.(png|jpg|jpeg|gif|svg|webp)$/i)?"image":e.match(/\.(woff|woff2|ttf|otf)$/i)?"font":"other"}checkThreshold(e,t,r){}generatePerformanceReport(){console.group("📊 性能监控报告"),this.metrics.firstContentfulPaint,this.metrics.largestContentfulPaint,this.metrics.firstInputDelay,this.metrics.cumulativeLayoutShift,this.metrics.domContentLoaded,this.metrics.loadComplete,this.analyzeSlowResources(),console.groupEnd()}analyzeSlowResources(){if(!this.metrics.resourceLoadTimes)return;const e=this.metrics.resourceLoadTimes.filter(t=>t.duration>o.RESOURCE_THRESHOLD).sort((t,r)=>r.duration-t.duration).slice(0,5);e.length>0&&e.forEach((t,r)=>{})}formatBytes(e){if(e===0)return"0 Bytes";const t=1024,r=["Bytes","KB","MB","GB"],s=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,s)).toFixed(2))+" "+r[s]}getMetrics(){return{...this.metrics}}destroy(){this.observers.forEach(e=>{e.disconnect()}),this.observers=[]}}let c=null;function l(){return c||(c=new m),c}export{m as PerformanceMonitor,l as initPerformanceMonitor};
