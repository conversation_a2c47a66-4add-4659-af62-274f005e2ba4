import{q as s,r as a}from"./element-plus-BiAL0NdQ.js";import{d as e,E as t,G as l,a as r,a1 as o,b as i,K as c,F as m,o as u}from"./vue-vendor-D6tHD5lA.js";import{_ as n}from"./index-CCmno-0X.js";const p={class:"carousel-item"},d=["src","alt"],v={class:"carousel-content"},_={class:"carousel-title"},f=n(e({__name:"Carousel",props:{items:{}},setup:e=>(e,n)=>{const f=a,g=s;return u(),t(g,{height:"340px",interval:5e3,arrow:"never",autoplay:!0},{default:l(()=>[(u(!0),r(m,null,o(e.items,s=>(u(),t(f,{key:s.title},{default:l(()=>[i("div",p,[i("img",{src:s.image,alt:s.title,class:"carousel-image"},null,8,d),i("div",v,[i("h2",_,c(s.title),1)])])]),_:2},1024))),128))]),_:1})}}),[["__scopeId","data-v-4705a81c"]]);export{f as C};
