import{_ as e,S as t}from"./index-CCmno-0X.js";import{d as r,a as s,L as a,u as c,b as i,K as p,o,c as n,D as u,C as d,I as l}from"./vue-vendor-D6tHD5lA.js";const m=e(r({__name:"CompanyTag",props:{enterpriseType:{type:String,default:"民企"}},setup(e){const t=e,{enterpriseType:r}=t;return(e,t)=>(o(),s("div",{class:"enterprise-type",style:a({"background-color":"国企"===c(r)?"#5fa4c0":"央企"===c(r)?"#418EAB":"民企"===c(r)?"#4BAA9C":"#5C6BC0"})},[i("p",null,p(c(r)),1)],4))}}),[["__scopeId","data-v-adefd1c0"]]),v={class:"product-image"},g=["src","alt"],y={class:"product-info"},f={class:"product-title"},C={class:"product-name"},w={class:"product-parameter"},N={key:0},P={class:"price-view-container"},_={class:"price-section"},I={class:"current-price"},U={class:"price-integer"},k={class:"price-decimal"},T={class:"price-unit",style:{"font-size":"14px"}},h={class:"view-section"},x={class:"view-count"},L={class:"enterprise-info"},W={class:"enterprise-info-container"},b={class:"enterprise-logo"},A=["src","alt"],B={key:1},S={class:"enterprise-name"},j={class:"enterprise-type"},M={class:"enterprise-info-arrow"},z=e(r({__name:"PropertyCard",props:{productId:{},productName:{},productImage:{},currentPrice:{},priceUnit:{},viewCount:{},enterpriseLogo:{},enterpriseName:{},enterpriseType:{},status:{default:"upcoming"},statusName:{},productCount:{},productCountUnit:{},productWeight:{},productWeightUnit:{},productPower:{},productPowerUnit:{}},emits:["click"],setup(e,{emit:r}){const a=e,c=r,z=n(()=>Math.floor(a.currentPrice)),D=n(()=>(100*(a.currentPrice-Math.floor(a.currentPrice))).toFixed(0).padStart(2,"0")),E=()=>{c("click",{productId:a.productId,productName:a.productName})};return(e,r)=>(o(),s("div",{class:"property-card",onClick:E},[i("div",{class:u(["status-tag",{"status-active":"5"===e.status,"status-inactive":"2"===e.status}])},[i("span",null,p(e.statusName),1)],2),i("div",v,[i("img",{src:e.productImage,alt:e.productName},null,8,g)]),i("div",y,[i("div",f,[i("div",C,p(e.productName),1),i("div",w,[i("span",null,p(e.productCount)+p(e.productCountUnit),1),i("span",null,p(e.productWeight)+p(e.productWeightUnit),1),e.productPower?(o(),s("span",N,p(e.productPower)+p(e.productPowerUnit),1)):d("",!0)])]),i("div",P,[i("div",_,[i("div",I,[r[0]||(r[0]=i("span",{class:"price-decimal"},"￥",-1)),i("span",U,p(z.value),1),i("span",k,"."+p(D.value),1),i("span",T,p(e.priceUnit),1)])]),i("div",h,[i("div",x,p(e.viewCount)+"人看过",1)])]),i("div",L,[i("div",W,[i("div",b,[e.enterpriseLogo?(o(),s("img",{key:0,src:e.enterpriseLogo,alt:e.enterpriseName},null,8,A)):(o(),s("span",B,p("upcoming"===e.status?"企业":"商家"),1))]),i("div",S,p(e.enterpriseName),1),i("div",j,[l(m,{enterpriseType:e.enterpriseType},null,8,["enterpriseType"])])]),i("div",M,[l(t,{iconName:"freedom-arrow",className:"arrow-icon"})])])])]))}}),[["__scopeId","data-v-2ea9f4b3"]]);export{m as C,z as P};
