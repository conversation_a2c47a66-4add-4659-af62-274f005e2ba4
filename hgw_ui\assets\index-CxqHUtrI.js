import{S as o,A as t,a as r,C as p}from"./index-CCmno-0X.js";import{M as s}from"./Modal-DCQzYcok.js";import{C as m}from"./Carousel-CdeEQzqk.js";import{P as e,C as n}from"./PropertyCard-BPAT2wDM.js";import{S as a}from"./ServiceSupport-VNFTK4S1.js";import{U as i}from"./UploadCard-GBXZ_Mqg.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./vue-vendor-D6tHD5lA.js";import"./pinia-DkXT_Xot.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vue-router-C0lzQS1p.js";import"./utils-common-PdkFOSu3.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./element-plus-BiAL0NdQ.js";import"./element-icons-Cfq32zG_.js";import"./vendor-lodash-D0OfQ6x6.js";import"./crypto-vendor-CkZKNqwc.js";import"./app-stores-CLUCXxRF.js";const c={install(c){c.component("SvgIcon",o),c.component("Modal",s),c.component("Carousel",m),c.component("PropertyCard",e),c.component("ServiceSupport",a),c.component("AuctionCard",t),c.component("AuctionListItem",r),c.component("CompanyTag",n),c.component("UploadCard",i),c.component("CustomScrollbar",p)}};export{c as default};
