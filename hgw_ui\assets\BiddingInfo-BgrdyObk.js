import{m as ee,l as te}from"./element-plus-DZBbDeaO.js";import{u as G,a as se}from"./vue-router-e9iWfNxP.js";import{d as U,r as C,Q as ie,c as p,t as N,s as W,z as T,x as oe,a as c,b as i,I as $,F as y,a1 as f,T as S,G as F,K as h,D as k,C as E,E as J,$ as ne,o as n,q as ae}from"./vue-vendor-2E6AJATX.js";import{_ as X,S as le,A as ce}from"./index-CkIekciI.js";import{a as re}from"./app-assets-WDvSAEaN.js";import{a as ue}from"./utils-common-CvYGMv_l.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./app-stores-DSLz6__G.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";const ve={class:"filter-container"},de={class:"filter-content"},pe={class:"labels-section"},_e={class:"options-section"},he={class:"province-row-group"},ye={class:"filter-options province-options first-row"},fe=["onClick"],me={key:0,class:"city-row first-row-cities"},ge={class:"filter-options city-options"},be=["onClick"],ke={class:"province-row-group"},Ce={class:"filter-options province-options second-row"},we=["onClick"],$e={key:0,class:"city-row second-row-cities"},xe={class:"filter-options city-options"},Re=["onClick"],Ie={class:"province-row-group"},Te={class:"filter-options province-options third-row"},Oe=["onClick"],Pe={key:0,class:"city-row third-row-cities"},je={class:"filter-options city-options"},Fe=["onClick"],Ne={class:"option-row"},qe={class:"filter-options status-options"},Be=["onClick"],ze={class:"selected-conditions"},Ae={class:"tags-container"},De={class:"result-count"},Le={class:"count-number"},Se=U({__name:"Filter",props:{total:{type:Number,default:0}},emits:["filter-change"],setup(M,{emit:q}){const O=G(),d=M,_=C(),g=C(),v=re,t=ie({province:"",city:"",keyword:"",page:1,status:"1,5,6",type:0}),u=C(""),P=p(()=>{const s=Math.ceil(w.length/3);return w.slice(0,s)}),a=p(()=>{const s=Math.ceil(w.length/3);return w.slice(s,s*2)}),m=p(()=>{const s=Math.ceil(w.length/3);return w.slice(s*2)}),x=p(()=>u.value==="first"&&t.province),r=p(()=>u.value==="second"&&t.province),V=p(()=>u.value==="third"&&t.province),B=p(()=>{if(!t.province||u.value!=="first")return[];const s=t.province.substring(0,2),o=Object.entries(v.area.city_list).filter(([l])=>l.substring(0,2)===s).map(([l,e])=>({label:e.replace(/[省市]$/g,""),value:l}));return[{label:"不限",value:""},...o]}),z=p(()=>{if(!t.province||u.value!=="second")return[];const s=t.province.substring(0,2),o=Object.entries(v.area.city_list).filter(([l])=>l.substring(0,2)===s).map(([l,e])=>({label:e.replace(/[省市]$/g,""),value:l}));return[{label:"不限",value:""},...o]}),A=p(()=>{if(!t.province||u.value!=="third")return[];const s=t.province.substring(0,2),o=Object.entries(v.area.city_list).filter(([l])=>l.substring(0,2)===s).map(([l,e])=>({label:e.replace(/[省市]$/g,""),value:l}));return[{label:"不限",value:""},...o]}),H=[{label:"不限",value:"1,5,6"},{label:"未开始",value:1},{label:"已成交",value:5},{label:"核实中",value:6}],w=[{label:"不限",value:""},...Object.entries(v.area.province_list).map(([s,o])=>({label:o.replace(/[省市]$/g,""),value:s}))];C(70);const R=()=>{_.value&&g.value&&T(()=>{const s=_.value.offsetHeight;g.value.style.height=`${s}px`})};let I=null;const Q=p(()=>{var o;const s=[];if(t.province){const l=v.area.province_list[t.province];l&&s.push({key:"province",label:l.replace(/[省市]$/g,"")})}if(t.city){const l=v.area.city_list[t.city];l&&s.push({key:"city",label:l.replace(/[省市]$/g,"")})}if(t.status&&t.status!=="1,5,6"){const l=(o=H.find(e=>e.value===t.status))==null?void 0:o.label;s.push({key:"status",label:l})}return s}),D=(s,o)=>{t.province=s,u.value=s?o:"",t.city="",j()},L=s=>{t.city=s,j()},Y=s=>{t.status=s,j()},Z=s=>{switch(s){case"type":t.type=0;break;case"province":t.province="",u.value="",t.city="";break;case"city":t.city="";break;case"status":t.status="1,5,6";break}j()},K=q,j=()=>{K("filter-change",{...t,selectedCount:Q.value.length})};return N(()=>t.province,()=>{T(()=>{R()})}),N(()=>t.province,()=>{T(()=>{R()})}),N([()=>B.value.length,()=>z.value.length,()=>A.value.length,u],()=>{T(()=>{R()})}),W(()=>{T(()=>{R(),_.value&&(I=new ResizeObserver(()=>{R()}),I.observe(_.value));const{name:s,type:o}=O.currentRoute.value.query;if(typeof s=="string"&&typeof o=="string"&&s in t&&(t[s]=o),t.province){const l=P.value.some(e=>e.value===t.province);u.value=l?"first":"second"}})}),oe(()=>{I&&(I.disconnect(),I=null)}),(s,o)=>{const l=ee;return n(),c("div",ve,[i("div",de,[i("div",pe,[i("div",{class:"label-item area",ref_key:"provinceTitleRef",ref:g},"所属省份",512),o[0]||(o[0]=i("div",{class:"label-item"},"标的状态",-1)),o[1]||(o[1]=i("div",{class:"label-item"},"已选条件",-1))]),o[4]||(o[4]=i("div",{class:"main-divider"},null,-1)),i("div",_e,[i("div",{class:"option-row province-city-container",ref_key:"provinceOptionsRef",ref:_,style:{"margin-top":"10px"}},[i("div",he,[i("div",ye,[(n(!0),c(y,null,f(P.value,e=>(n(),c("div",{key:e.value,class:k(["filter-option province-option",{active:t.province===e.value}]),onClick:b=>D(e.value,"first")},h(e.label),11,fe))),128))]),$(S,{name:"city-slide"},{default:F(()=>[x.value&&B.value.length>1?(n(),c("div",me,[i("div",ge,[(n(!0),c(y,null,f(B.value,e=>(n(),c("div",{key:e.value,class:k(["filter-option city-option",{active:t.city===e.value}]),onClick:b=>L(e.value)},h(e.label),11,be))),128))])])):E("",!0)]),_:1})]),i("div",ke,[i("div",Ce,[(n(!0),c(y,null,f(a.value,e=>(n(),c("div",{key:e.value,class:k(["filter-option province-option",{active:t.province===e.value}]),onClick:b=>D(e.value,"second")},h(e.label),11,we))),128))]),$(S,{name:"city-slide"},{default:F(()=>[r.value&&z.value.length>1?(n(),c("div",$e,[i("div",xe,[(n(!0),c(y,null,f(z.value,e=>(n(),c("div",{key:e.value,class:k(["filter-option city-option",{active:t.city===e.value}]),onClick:b=>L(e.value)},h(e.label),11,Re))),128))])])):E("",!0)]),_:1})]),i("div",Ie,[i("div",Te,[(n(!0),c(y,null,f(m.value,e=>(n(),c("div",{key:e.value,class:k(["filter-option province-option",{active:t.province===e.value}]),onClick:b=>D(e.value,"third")},h(e.label),11,Oe))),128))]),$(S,{name:"city-slide"},{default:F(()=>[V.value&&A.value.length>1?(n(),c("div",Pe,[i("div",je,[(n(!0),c(y,null,f(A.value,e=>(n(),c("div",{key:e.value,class:k(["filter-option city-option",{active:t.city===e.value}]),onClick:b=>L(e.value)},h(e.label),11,Fe))),128))])])):E("",!0)]),_:1})])],512),i("div",Ne,[i("div",qe,[(n(),c(y,null,f(H,e=>i("div",{key:e.value,class:k(["filter-option",{active:t.status===e.value}]),onClick:b=>Y(e.value)},h(e.label),11,Be)),64))])]),i("div",ze,[i("div",Ae,[(n(!0),c(y,null,f(Q.value,e=>(n(),J(l,{key:e.key,closable:"",onClose:b=>Z(e.key),class:"condition-tag"},{default:F(()=>[ne(h(e.label),1)]),_:2},1032,["onClose"]))),128))]),i("div",De,[o[2]||(o[2]=i("span",{class:"count-text"},"灰谷网为你筛选了",-1)),i("span",Le,h(d.total),1),o[3]||(o[3]=i("span",{class:"count-text"},"条相关结果",-1))])])])])])}}}),Ee=X(Se,[["__scopeId","data-v-8d14c51f"]]),Me={class:"info"},Ve={class:"data-list"},He={key:0},Qe={class:"auction-cards"},Ge={class:"pagination"},Ue={key:1,class:"empty"},We={class:"empty-icon"},Je=U({__name:"BiddingInfo",setup(M){const q=G(),O=se(),d=C({province:"",city:"",keyword:"",page:1,status:"1,5,6",type:0}),_=C([]),g=C(0),v=async()=>{try{const a=await ue.getAuctionList(d.value);if(a.code==1){const m=a.data.data.map(async r=>({id:r.id,pmhId:r.pmh_id,bdName:r.bd_title,startTime:r.start_time_name,endTime:r.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+r.bd_url,bdQipaijia:r.bd_qipaijia,qpjDanwie:r.qpj_danwie,bdWeiguan:r.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:r.bd_status})),x=await Promise.all(m);_.value=x,g.value=a.data.total}}catch(a){console.error("获取数据失败:",a)}};N(()=>O.query.keyword,a=>{a&&typeof a=="string"&&(d.value.keyword=a,d.value.page=1,v())},{immediate:!0}),W(()=>{const a=O.query.keyword;a&&typeof a=="string"&&(d.value.keyword=a),v()});const t=a=>{q.push({name:"auctionDetail",query:{id:a.productId,pmhId:a.pmhId,crumbsTitle:"标的信息"}})},u=a=>{d.value=a,v()},P=a=>{d.value.page=a,v()};return(a,m)=>{const x=te;return n(),c("div",Me,[$(Ee,{onFilterChange:u,total:g.value,page:d.value.page},null,8,["total","page"]),m[1]||(m[1]=i("div",{class:"divider"},null,-1)),i("div",Ve,[_.value.length!==0?(n(),c("div",He,[i("div",Qe,[(n(!0),c(y,null,f(_.value,r=>(n(),J(ce,ae({key:r.id},{ref_for:!0},r,{onClick:t}),null,16))),128))]),i("div",Ge,[$(x,{"current-page":d.value.page,total:g.value,"page-size":16,layout:"total, prev, pager, next, jumper",onCurrentChange:P},null,8,["current-page","total"])])])):(n(),c("div",Ue,[i("div",We,[$(le,{iconName:"search",className:"empty-search-icon"})]),m[0]||(m[0]=i("div",{class:"empty-text"},"没有找到相关拍品",-1))]))])])}}}),dt=X(Je,[["__scopeId","data-v-4ce5ed29"]]);export{dt as default};
