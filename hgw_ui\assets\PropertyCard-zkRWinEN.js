import{_,S as y}from"./index-CkIekciI.js";import{d as v,a as r,L as C,u as n,b as e,K as t,o as i,c as l,D as f,C as N,I as m}from"./vue-vendor-2E6AJATX.js";const w=v({__name:"CompanyTag",props:{enterpriseType:{type:String,default:"民企"}},setup(c){const a=c,{enterpriseType:o}=a;return(p,d)=>(i(),r("div",{class:"enterprise-type",style:C({"background-color":n(o)==="国企"?"#5fa4c0":n(o)==="央企"?"#418EAB":n(o)==="民企"?"#4BAA9C":"#5C6BC0"})},[e("p",null,t(n(o)),1)],4))}}),P=_(w,[["__scopeId","data-v-adefd1c0"]]),k={class:"product-image"},I=["src","alt"],T={class:"product-info"},U={class:"product-title"},B={class:"product-name"},S={class:"product-parameter"},L={key:0},W={class:"price-view-container"},b={class:"price-section"},z={class:"current-price"},A={class:"price-integer"},D={class:"price-decimal"},V={class:"price-unit",style:{"font-size":"14px"}},E={class:"view-section"},M={class:"view-count"},F={class:"enterprise-info"},K={class:"enterprise-info-container"},$={class:"enterprise-logo"},j=["src","alt"],q={key:1},G={class:"enterprise-name"},H={class:"enterprise-type"},J={class:"enterprise-info-arrow"},O=v({__name:"PropertyCard",props:{productId:{},productName:{},productImage:{},currentPrice:{},priceUnit:{},viewCount:{},enterpriseLogo:{},enterpriseName:{},enterpriseType:{},status:{default:"upcoming"},statusName:{},productCount:{},productCountUnit:{},productWeight:{},productWeightUnit:{},productPower:{},productPowerUnit:{}},emits:["click"],setup(c,{emit:a}){const o=c,p=a,d=l(()=>Math.floor(o.currentPrice)),h=l(()=>((o.currentPrice-Math.floor(o.currentPrice))*100).toFixed(0).padStart(2,"0")),g=()=>{p("click",{productId:o.productId,productName:o.productName})};return(s,u)=>(i(),r("div",{class:"property-card",onClick:g},[e("div",{class:f(["status-tag",{"status-active":s.status==="5","status-inactive":s.status==="2"}])},[e("span",null,t(s.statusName),1)],2),e("div",k,[e("img",{src:s.productImage,alt:s.productName},null,8,I)]),e("div",T,[e("div",U,[e("div",B,t(s.productName),1),e("div",S,[e("span",null,t(s.productCount)+t(s.productCountUnit),1),e("span",null,t(s.productWeight)+t(s.productWeightUnit),1),s.productPower?(i(),r("span",L,t(s.productPower)+t(s.productPowerUnit),1)):N("",!0)])]),e("div",W,[e("div",b,[e("div",z,[u[0]||(u[0]=e("span",{class:"price-decimal"},"￥",-1)),e("span",A,t(d.value),1),e("span",D,"."+t(h.value),1),e("span",V,t(s.priceUnit),1)])]),e("div",E,[e("div",M,t(s.viewCount)+"人看过",1)])]),e("div",F,[e("div",K,[e("div",$,[s.enterpriseLogo?(i(),r("img",{key:0,src:s.enterpriseLogo,alt:s.enterpriseName},null,8,j)):(i(),r("span",q,t(s.status==="upcoming"?"企业":"商家"),1))]),e("div",G,t(s.enterpriseName),1),e("div",H,[m(P,{enterpriseType:s.enterpriseType},null,8,["enterpriseType"])])]),e("div",J,[m(y,{iconName:"freedom-arrow",className:"arrow-icon"})])])])]))}}),X=_(O,[["__scopeId","data-v-2ea9f4b3"]]);export{P as C,X as P};
