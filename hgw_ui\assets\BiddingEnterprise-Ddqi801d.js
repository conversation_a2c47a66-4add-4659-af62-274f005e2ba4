import{M as Y}from"./Modal-D0LrqBzM.js";import{k as Z,d as ee,f as te,e as se,n as oe,o as ae,b as ne,E as k}from"./element-plus-DZBbDeaO.js";import{_ as P,S as ie}from"./index-CkIekciI.js";import{d as R,c as L,a as p,D as V,b as e,K as w,o as c,F as C,a1 as $,u as I,I as s,r as v,Q as le,s as re,$ as O,G as u,z as ce,E as me}from"./vue-vendor-2E6AJATX.js";import{d as F,z as S,y as j,x as B,b as M,c as T,t as r,g as d}from"./app-assets-WDvSAEaN.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vue-router-e9iWfNxP.js";import"./utils-common-CvYGMv_l.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";import"./app-stores-DSLz6__G.js";const ue={class:"image-section"},de=["src"],pe={class:"content-section"},ge={class:"card-title"},_e={class:"card-title-text"},fe={class:"card-subtitle"},be={class:"card-description"},ve={class:"card-number"},he=R({__name:"AdvantageCard",props:{number:{},title:{},subtitle:{},description:{},imgUrl:{}},setup(x){const g=x,_=L(()=>{const i=g.number%2===0;return{"card-even":i,"card-odd":!i}}),f=L(()=>g.number.toString().padStart(2,"0"));return(i,y)=>(c(),p("div",{class:V(["advantage-card",_.value])},[e("div",ue,[e("img",{src:i.imgUrl,alt:"",class:"card-icon"},null,8,de)]),e("div",pe,[e("div",ge,[e("span",_e,w(i.title),1),e("h4",fe,w(i.subtitle),1),e("p",be,w(i.description),1)]),e("span",ve,w(f.value),1)])],2))}}),ye=P(he,[["__scopeId","data-v-28b88ec1"]]),Ve={class:"partner-scroll"},Ne={class:"scroll-row"},we={class:"scroll-content scroll-left-to-right"},Ce={class:"scroll-row"},$e={class:"scroll-content scroll-right-to-left"},xe={class:"scroll-row"},Ee={class:"scroll-content scroll-left-to-right"},Ue=R({__name:"PartnerScroll",setup(x){const g=[{name:"大连水泥集团有限公司",icon:F},{name:"中国中铁",icon:S},{name:"中国有色金属集团",icon:j},{name:"徐钢集团",icon:B},{name:"大连水泥集团有限公司",icon:F},{name:"中国中铁",icon:S},{name:"中国有色金属集团",icon:j},{name:"徐钢集团",icon:B},{name:"大连水泥集团有限公司",icon:F},{name:"中国中铁",icon:S},{name:"中国有色金属集团",icon:j},{name:"徐钢集团",icon:B},{name:"宇通集团",icon:M},{name:"新华冶金",icon:T},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"宇通集团",icon:M},{name:"新华冶金",icon:T},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"宇通集团",icon:M},{name:"新华冶金",icon:T},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"天瑞集团",icon:r},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"天瑞集团",icon:r},{name:"国泰",icon:d},{name:"天瑞集团",icon:r},{name:"国泰",icon:d}],_=g.slice(0,11),f=g.slice(11,22),i=g.slice(22,33);return(y,E)=>{const h=Z;return c(),p("div",Ve,[e("div",Ne,[e("div",we,[(c(!0),p(C,null,$(I(_),(l,o)=>(c(),p("div",{key:`first-${o}`,class:"partner-card"},[s(h,{src:l.icon,class:"partner-logo"},null,8,["src"])]))),128))])]),e("div",Ce,[e("div",$e,[(c(!0),p(C,null,$(I(f),(l,o)=>(c(),p("div",{key:`second-${o}`,class:"partner-card"},[s(h,{src:l.icon,class:"partner-logo"},null,8,["src"])]))),128))])]),e("div",xe,[e("div",Ee,[(c(!0),p(C,null,$(I(i),(l,o)=>(c(),p("div",{key:`third-${o}`,class:"partner-card"},[s(h,{src:l.icon,class:"partner-logo"},null,8,["src"])]))),128))])])])}}}),ke=P(Ue,[["__scopeId","data-v-20c74e81"]]),Ie={class:"enterprise-cooperation"},Fe={class:"advantages-section"},Se={class:"container"},je={class:"advantages-grid"},Be={class:"partners-section"},Me={class:"cooperation-form"},Te={class:"form-content"},Pe={class:"disposal-cycle-options",style:{width:"100%"}},Re={class:"form-submit"},ze=R({__name:"BiddingEnterprise",setup(x){const g=v([{title:"企业信息推广",subtitle:"全网覆盖 立体传播",description:"充分发挥网络平台优势，为企业提供全方位多层次的信息发布服务，让企业信息得到最大化传播，提升企业知名度和影响力。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img1_1754959891411.png"},{title:"资产招商服务",subtitle:"精准匹配 高效对接",description:"基于大数据分析和智能匹配算法，为企业提供精准的资产招商服务，实现供需双方的高效对接，降低交易成本，提高成交效率。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img2_1754959911419.png"},{title:"司法竞价/拍卖服务",subtitle:"合规透明 专业高效",description:"严格按照司法程序和相关法律法规，提供专业的竞价拍卖服务，确保交易过程公开透明，维护各方合法权益。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img3_1754959925802.png"},{title:"平台对接",subtitle:"多元整合 协同发展",description:"整合多方资源，建立完善的平台对接机制，实现信息共享、资源互补，为企业提供一站式综合服务解决方案。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img4_1754959939308.png"},{title:"灰谷智能（数字DM）服务",subtitle:"智能驱动 数字化运营",description:"运用人工智能和大数据技术，为企业提供智能化的数字营销解决方案，助力企业实现数字化转型升级。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img5_1754959955679.png"},{title:"定制化服务",subtitle:"量身定制 精准服务",description:"根据企业具体需求和行业特点，提供个性化的定制服务方案，确保服务内容与企业发展战略高度契合。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img6_1754959967912.png"}]),_=v(!1),f=v(!1),i=v(!1),y=v([]),E=v(!1),h=v(!1),l=v(),o=le({contactName:"",phoneNumber:"",companyName:"",assetName:"",assetValue:"",disposalCycle:""}),q={contactName:[{required:!0,message:"请填写联系人姓名",trigger:"blur"},{min:2,max:20,message:"联系人姓名长度在 2 到 20 个字符",trigger:"blur"}],phoneNumber:[{required:!0,message:"请填写手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请填写正确的手机号格式",trigger:"blur"}],companyName:[{min:2,max:50,message:"公司名称长度在 2 到 50 个字符",trigger:"blur"}],assetName:[{min:2,max:100,message:"处置物资名称长度在 2 到 100 个字符",trigger:"blur"}],assetValue:[{min:1,max:50,message:"处置物资价值长度在 1 到 50 个字符",trigger:"blur"}],disposalCycle:[{required:!1,message:"请选择处置周期",trigger:"change"}]},D=()=>{_.value=!0},z=async()=>{if(l.value)try{await l.value.validate(),f.value=!0,setTimeout(()=>{k.success("提交成功！我们的专属顾问会尽快联系您"),f.value=!1,_.value=!1,A()},2e3)}catch{k.error("请检查表单信息是否填写正确")}},A=()=>{l.value&&l.value.resetFields(),Object.assign(o,{contactName:"",phoneNumber:"",companyName:"",assetName:"",assetValue:"",disposalCycle:""})},G=()=>{A(),k.info("已取消操作")},H=()=>{const U=new IntersectionObserver(t=>{t.forEach(m=>{if(m.isIntersecting){const n=m.target;if(n.classList.contains("section-header")&&(n.closest(".advantages-section")?i.value=!0:n.closest(".partners-section")&&(E.value=!0)),n.classList.contains("advantage-item")){const b=parseInt(n.dataset.index||"0");setTimeout(()=>{y.value[b]=!0},b)}n.classList.contains("cooperation-button-wrapper")&&setTimeout(()=>{h.value=!0},600)}})},{threshold:0,rootMargin:"0px 0px 0px 0px"});ce(()=>{[".section-header",".advantage-item",".cooperation-button-wrapper"].forEach(m=>{document.querySelectorAll(m).forEach(b=>U.observe(b))})})},K=()=>{y.value=new Array(g.value.length).fill(!1),setTimeout(()=>{i.value=!0},300)};return re(()=>{K(),H()}),(U,t)=>{const m=se,n=te,b=ae,Q=oe,J=ee,W=ne,X=Y;return c(),p("div",Ie,[e("section",Fe,[e("div",Se,[e("div",{class:V(["section-header",{"animate-fade-in-up":i.value}])},t[7]||(t[7]=[e("p",{class:"section-title"},"我们的优势",-1),e("p",{class:"section-subtitle"},"数字化管理处置闲废物资全链路方案服务商",-1)]),2),e("div",je,[(c(!0),p(C,null,$(g.value,(a,N)=>(c(),me(ye,{key:N,number:N+1,title:a.title,subtitle:a.subtitle,description:a.description,"img-url":a.imgUrl,class:V(["advantage-item",{"animate-slide-in":y.value[N]}]),"data-index":N},null,8,["number","title","subtitle","description","img-url","class","data-index"]))),128))]),e("div",{class:V(["cooperation-button-wrapper",{"animate-bounce-in":h.value}])},[e("button",{class:"cooperation-button",onClick:D},[s(ie,{iconName:"enterpriseCooperation-cooperation",className:"button-icon"}),t[8]||(t[8]=O(" 携手合作 "))])],2)])]),e("section",Be,[e("div",{class:V(["section-header",{"animate-fade-in-up":E.value}])},t[9]||(t[9]=[e("p",{class:"section-title"},"我们的合作伙伴",-1),e("p",{class:"section-subtitle"},"遍地全国的合作伙伴是我们实力的展现",-1)]),2),s(ke)]),s(X,{modelValue:_.value,"onUpdate:modelValue":t[6]||(t[6]=a=>_.value=a),title:"携手合作","title-icon":"model-icon",width:"64.6vw","confirm-loading":f.value,"show-footer":!1,"confirm-button-text":"提交资料",onConfirm:z,onCancel:G,class:"cooperation-modal"},{default:u(()=>[e("div",Me,[t[11]||(t[11]=e("div",{class:"form-header"},[e("p",{class:"form-title"},"留下您的联系方式 专属顾问会尽快联系您")],-1)),e("div",Te,[s(J,{ref_key:"cooperationFormRef",ref:l,model:o,rules:q,"label-position":"right","label-width":"140px",class:"cooperation-form-el"},{default:u(()=>[s(n,{label:"联系人",prop:"contactName",class:"form-item"},{default:u(()=>[s(m,{modelValue:o.contactName,"onUpdate:modelValue":t[0]||(t[0]=a=>o.contactName=a),placeholder:"请填写您的姓名（必填项）",class:"form-input"},null,8,["modelValue"])]),_:1}),s(n,{label:"手机号",prop:"phoneNumber",class:"form-item"},{default:u(()=>[s(m,{modelValue:o.phoneNumber,"onUpdate:modelValue":t[1]||(t[1]=a=>o.phoneNumber=a),placeholder:"请填写您的手机号（必填项）",class:"form-input"},null,8,["modelValue"])]),_:1}),s(n,{label:"公司名称",prop:"companyName",class:"form-item full-width"},{default:u(()=>[s(m,{modelValue:o.companyName,"onUpdate:modelValue":t[2]||(t[2]=a=>o.companyName=a),placeholder:"请填写您的公司名称",class:"form-input"},null,8,["modelValue"])]),_:1}),s(n,{label:"处置物资名称",prop:"assetName",class:"form-item full-width"},{default:u(()=>[s(m,{modelValue:o.assetName,"onUpdate:modelValue":t[3]||(t[3]=a=>o.assetName=a),placeholder:"请填写您处置物资名称，如：钢铁、铜铝、报废汽车",class:"form-input"},null,8,["modelValue"])]),_:1}),s(n,{label:"单次处置物资价值",prop:"assetValue",class:"form-item full-width"},{default:u(()=>[s(m,{modelValue:o.assetValue,"onUpdate:modelValue":t[4]||(t[4]=a=>o.assetValue=a),placeholder:"请填写您的单次处置物资价值估值",class:"form-input"},null,8,["modelValue"])]),_:1}),s(n,{label:"处置周期",prop:"disposalCycle",class:"form-item full-width"},{default:u(()=>[e("div",Pe,[s(Q,{modelValue:o.disposalCycle,"onUpdate:modelValue":t[5]||(t[5]=a=>o.disposalCycle=a),placeholder:"请选择您的处置周期",class:"form-select","popper-class":"custom-popper"},{default:u(()=>[s(b,{label:"一次性",value:"一次性"}),s(b,{label:"长期试探",value:"长期试探"}),s(b,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])])]),_:1})]),_:1},8,["model"]),e("div",Re,[s(W,{type:"primary",size:"large",loading:f.value,onClick:z,plain:"",class:"submit-button"},{default:u(()=>t[10]||(t[10]=[O(" 提交资料 ")])),_:1,__:[10]},8,["loading"])])])])]),_:1},8,["modelValue","confirm-loading"])])}}}),ot=P(ze,[["__scopeId","data-v-f541c4ab"]]);export{ot as default};
