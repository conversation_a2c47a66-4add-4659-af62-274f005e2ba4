import{M as e}from"./Modal-DCQzYcok.js";import{k as a,d as s,f as t,e as l,n as i,o,b as n,E as c}from"./element-plus-BiAL0NdQ.js";import{_ as r,S as m}from"./index-CCmno-0X.js";import{d as u,c as p,a as d,D as g,b as v,K as b,o as f,F as h,a1 as y,u as _,I as V,r as N,Q as j,s as w,$ as x,G as U,z as C,E as k}from"./vue-vendor-D6tHD5lA.js";import{d as E,z as I,y as S,x as $,b as q,c as M,t as T,g as z}from"./app-assets-DXWh4tep.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vue-router-C0lzQS1p.js";import"./utils-common-PdkFOSu3.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";import"./app-stores-CLUCXxRF.js";const A={class:"image-section"},F=["src"],L={class:"content-section"},D={class:"card-title"},O={class:"card-title-text"},B={class:"card-subtitle"},G={class:"card-description"},K={class:"card-number"},P=r(u({__name:"AdvantageCard",props:{number:{},title:{},subtitle:{},description:{},imgUrl:{}},setup(e){const a=e,s=p(()=>{const e=a.number%2==0;return{"card-even":e,"card-odd":!e}}),t=p(()=>a.number.toString().padStart(2,"0"));return(e,a)=>(f(),d("div",{class:g(["advantage-card",s.value])},[v("div",A,[v("img",{src:e.imgUrl,alt:"",class:"card-icon"},null,8,F)]),v("div",L,[v("div",D,[v("span",O,b(e.title),1),v("h4",B,b(e.subtitle),1),v("p",G,b(e.description),1)]),v("span",K,b(t.value),1)])],2))}}),[["__scopeId","data-v-28b88ec1"]]),Q={class:"partner-scroll"},R={class:"scroll-row"},H={class:"scroll-content scroll-left-to-right"},J={class:"scroll-row"},W={class:"scroll-content scroll-right-to-left"},X={class:"scroll-row"},Y={class:"scroll-content scroll-left-to-right"},Z=r(u({__name:"PartnerScroll",setup(e){const s=[{name:"大连水泥集团有限公司",icon:E},{name:"中国中铁",icon:I},{name:"中国有色金属集团",icon:S},{name:"徐钢集团",icon:$},{name:"大连水泥集团有限公司",icon:E},{name:"中国中铁",icon:I},{name:"中国有色金属集团",icon:S},{name:"徐钢集团",icon:$},{name:"大连水泥集团有限公司",icon:E},{name:"中国中铁",icon:I},{name:"中国有色金属集团",icon:S},{name:"徐钢集团",icon:$},{name:"宇通集团",icon:q},{name:"新华冶金",icon:M},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"宇通集团",icon:q},{name:"新华冶金",icon:M},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"宇通集团",icon:q},{name:"新华冶金",icon:M},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"天瑞集团",icon:T},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"天瑞集团",icon:T},{name:"国泰",icon:z},{name:"天瑞集团",icon:T},{name:"国泰",icon:z}],t=s.slice(0,11),l=s.slice(11,22),i=s.slice(22,33);return(e,s)=>{const o=a;return f(),d("div",Q,[v("div",R,[v("div",H,[(f(!0),d(h,null,y(_(t),(e,a)=>(f(),d("div",{key:`first-${a}`,class:"partner-card"},[V(o,{src:e.icon,class:"partner-logo"},null,8,["src"])]))),128))])]),v("div",J,[v("div",W,[(f(!0),d(h,null,y(_(l),(e,a)=>(f(),d("div",{key:`second-${a}`,class:"partner-card"},[V(o,{src:e.icon,class:"partner-logo"},null,8,["src"])]))),128))])]),v("div",X,[v("div",Y,[(f(!0),d(h,null,y(_(i),(e,a)=>(f(),d("div",{key:`third-${a}`,class:"partner-card"},[V(o,{src:e.icon,class:"partner-logo"},null,8,["src"])]))),128))])])])}}}),[["__scopeId","data-v-20c74e81"]]),ee={class:"enterprise-cooperation"},ae={class:"advantages-section"},se={class:"container"},te={class:"advantages-grid"},le={class:"partners-section"},ie={class:"cooperation-form"},oe={class:"form-content"},ne={class:"disposal-cycle-options",style:{width:"100%"}},ce={class:"form-submit"},re=r(u({__name:"BiddingEnterprise",setup(a){const r=N([{title:"企业信息推广",subtitle:"全网覆盖 立体传播",description:"充分发挥网络平台优势，为企业提供全方位多层次的信息发布服务，让企业信息得到最大化传播，提升企业知名度和影响力。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img1_1754959891411.png"},{title:"资产招商服务",subtitle:"精准匹配 高效对接",description:"基于大数据分析和智能匹配算法，为企业提供精准的资产招商服务，实现供需双方的高效对接，降低交易成本，提高成交效率。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img2_1754959911419.png"},{title:"司法竞价/拍卖服务",subtitle:"合规透明 专业高效",description:"严格按照司法程序和相关法律法规，提供专业的竞价拍卖服务，确保交易过程公开透明，维护各方合法权益。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img3_1754959925802.png"},{title:"平台对接",subtitle:"多元整合 协同发展",description:"整合多方资源，建立完善的平台对接机制，实现信息共享、资源互补，为企业提供一站式综合服务解决方案。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img4_1754959939308.png"},{title:"灰谷智能（数字DM）服务",subtitle:"智能驱动 数字化运营",description:"运用人工智能和大数据技术，为企业提供智能化的数字营销解决方案，助力企业实现数字化转型升级。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img5_1754959955679.png"},{title:"定制化服务",subtitle:"量身定制 精准服务",description:"根据企业具体需求和行业特点，提供个性化的定制服务方案，确保服务内容与企业发展战略高度契合。",imgUrl:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/img6_1754959967912.png"}]),u=N(!1),p=N(!1),b=N(!1),_=N([]),E=N(!1),I=N(!1),S=N(),$=j({contactName:"",phoneNumber:"",companyName:"",assetName:"",assetValue:"",disposalCycle:""}),q={contactName:[{required:!0,message:"请填写联系人姓名",trigger:"blur"},{min:2,max:20,message:"联系人姓名长度在 2 到 20 个字符",trigger:"blur"}],phoneNumber:[{required:!0,message:"请填写手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请填写正确的手机号格式",trigger:"blur"}],companyName:[{min:2,max:50,message:"公司名称长度在 2 到 50 个字符",trigger:"blur"}],assetName:[{min:2,max:100,message:"处置物资名称长度在 2 到 100 个字符",trigger:"blur"}],assetValue:[{min:1,max:50,message:"处置物资价值长度在 1 到 50 个字符",trigger:"blur"}],disposalCycle:[{required:!1,message:"请选择处置周期",trigger:"change"}]},M=()=>{u.value=!0},T=async()=>{if(S.value)try{await S.value.validate(),p.value=!0,setTimeout(()=>{c.success("提交成功！我们的专属顾问会尽快联系您"),p.value=!1,u.value=!1,z()},2e3)}catch(e){c.error("请检查表单信息是否填写正确")}},z=()=>{S.value&&S.value.resetFields(),Object.assign($,{contactName:"",phoneNumber:"",companyName:"",assetName:"",assetValue:"",disposalCycle:""})},A=()=>{z(),c.info("已取消操作")};return w(()=>{_.value=new Array(r.value.length).fill(!1),setTimeout(()=>{b.value=!0},300),(()=>{const e=new IntersectionObserver(e=>{e.forEach(e=>{if(e.isIntersecting){const a=e.target;if(a.classList.contains("section-header")&&(a.closest(".advantages-section")?b.value=!0:a.closest(".partners-section")&&(E.value=!0)),a.classList.contains("advantage-item")){const e=parseInt(a.dataset.index||"0");setTimeout(()=>{_.value[e]=!0},e)}a.classList.contains("cooperation-button-wrapper")&&setTimeout(()=>{I.value=!0},600)}})},{threshold:0,rootMargin:"0px 0px 0px 0px"});C(()=>{[".section-header",".advantage-item",".cooperation-button-wrapper"].forEach(a=>{document.querySelectorAll(a).forEach(a=>e.observe(a))})})})()}),(a,c)=>{const N=l,j=t,w=o,C=i,z=s,F=n,L=e;return f(),d("div",ee,[v("section",ae,[v("div",se,[v("div",{class:g(["section-header",{"animate-fade-in-up":b.value}])},c[7]||(c[7]=[v("p",{class:"section-title"},"我们的优势",-1),v("p",{class:"section-subtitle"},"数字化管理处置闲废物资全链路方案服务商",-1)]),2),v("div",te,[(f(!0),d(h,null,y(r.value,(e,a)=>(f(),k(P,{key:a,number:a+1,title:e.title,subtitle:e.subtitle,description:e.description,"img-url":e.imgUrl,class:g(["advantage-item",{"animate-slide-in":_.value[a]}]),"data-index":a},null,8,["number","title","subtitle","description","img-url","class","data-index"]))),128))]),v("div",{class:g(["cooperation-button-wrapper",{"animate-bounce-in":I.value}])},[v("button",{class:"cooperation-button",onClick:M},[V(m,{iconName:"enterpriseCooperation-cooperation",className:"button-icon"}),c[8]||(c[8]=x(" 携手合作 "))])],2)])]),v("section",le,[v("div",{class:g(["section-header",{"animate-fade-in-up":E.value}])},c[9]||(c[9]=[v("p",{class:"section-title"},"我们的合作伙伴",-1),v("p",{class:"section-subtitle"},"遍地全国的合作伙伴是我们实力的展现",-1)]),2),V(Z)]),V(L,{modelValue:u.value,"onUpdate:modelValue":c[6]||(c[6]=e=>u.value=e),title:"携手合作","title-icon":"model-icon",width:"64.6vw","confirm-loading":p.value,"show-footer":!1,"confirm-button-text":"提交资料",onConfirm:T,onCancel:A,class:"cooperation-modal"},{default:U(()=>[v("div",ie,[c[11]||(c[11]=v("div",{class:"form-header"},[v("p",{class:"form-title"},"留下您的联系方式 专属顾问会尽快联系您")],-1)),v("div",oe,[V(z,{ref_key:"cooperationFormRef",ref:S,model:$,rules:q,"label-position":"right","label-width":"140px",class:"cooperation-form-el"},{default:U(()=>[V(j,{label:"联系人",prop:"contactName",class:"form-item"},{default:U(()=>[V(N,{modelValue:$.contactName,"onUpdate:modelValue":c[0]||(c[0]=e=>$.contactName=e),placeholder:"请填写您的姓名（必填项）",class:"form-input"},null,8,["modelValue"])]),_:1}),V(j,{label:"手机号",prop:"phoneNumber",class:"form-item"},{default:U(()=>[V(N,{modelValue:$.phoneNumber,"onUpdate:modelValue":c[1]||(c[1]=e=>$.phoneNumber=e),placeholder:"请填写您的手机号（必填项）",class:"form-input"},null,8,["modelValue"])]),_:1}),V(j,{label:"公司名称",prop:"companyName",class:"form-item full-width"},{default:U(()=>[V(N,{modelValue:$.companyName,"onUpdate:modelValue":c[2]||(c[2]=e=>$.companyName=e),placeholder:"请填写您的公司名称",class:"form-input"},null,8,["modelValue"])]),_:1}),V(j,{label:"处置物资名称",prop:"assetName",class:"form-item full-width"},{default:U(()=>[V(N,{modelValue:$.assetName,"onUpdate:modelValue":c[3]||(c[3]=e=>$.assetName=e),placeholder:"请填写您处置物资名称，如：钢铁、铜铝、报废汽车",class:"form-input"},null,8,["modelValue"])]),_:1}),V(j,{label:"单次处置物资价值",prop:"assetValue",class:"form-item full-width"},{default:U(()=>[V(N,{modelValue:$.assetValue,"onUpdate:modelValue":c[4]||(c[4]=e=>$.assetValue=e),placeholder:"请填写您的单次处置物资价值估值",class:"form-input"},null,8,["modelValue"])]),_:1}),V(j,{label:"处置周期",prop:"disposalCycle",class:"form-item full-width"},{default:U(()=>[v("div",ne,[V(C,{modelValue:$.disposalCycle,"onUpdate:modelValue":c[5]||(c[5]=e=>$.disposalCycle=e),placeholder:"请选择您的处置周期",class:"form-select","popper-class":"custom-popper"},{default:U(()=>[V(w,{label:"一次性",value:"一次性"}),V(w,{label:"长期试探",value:"长期试探"}),V(w,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])])]),_:1})]),_:1},8,["model"]),v("div",ce,[V(F,{type:"primary",size:"large",loading:p.value,onClick:T,plain:"",class:"submit-button"},{default:U(()=>c[10]||(c[10]=[x(" 提交资料 ")])),_:1,__:[10]},8,["loading"])])])])]),_:1},8,["modelValue","confirm-loading"])])}}}),[["__scopeId","data-v-f541c4ab"]]);export{re as default};
