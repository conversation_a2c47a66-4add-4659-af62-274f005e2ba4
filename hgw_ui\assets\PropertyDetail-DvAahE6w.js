import{M as We}from"./Modal-D0LrqBzM.js";import{b as je,E as J}from"./element-plus-DZBbDeaO.js";import{a as Ae,u as Ve}from"./vue-router-e9iWfNxP.js";import{S as g,_ as Xe}from"./index-CkIekciI.js";import{C as Oe,P as Fe}from"./PropertyCard-zkRWinEN.js";import{n as U}from"./utils-common-CvYGMv_l.js";import{d as ze,r as i,c as le,s as Ge,z as Ke,x as Je,a as m,b as e,I as v,u as re,F as D,K as u,C as R,a1 as Q,L as Z,$ as ce,D as N,G as ue,E as Qe,q as Ze,o as d}from"./vue-vendor-2E6AJATX.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./app-stores-DSLz6__G.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";const et={class:"property-detail"},tt={class:"header-section"},st={class:"breadcrumb"},ot={class:"current"},at={class:"header-right"},nt={class:"collect-text"},it={class:"view-count"},lt={class:"content-section"},rt={class:"image-list-container"},ct=["onMousemove"],ut=["src","alt"],pt={class:"image-preview"},dt={class:"preview-container"},mt={class:"image-container"},vt=["src","alt"],gt={key:0,class:"magnified-view"},ft=["src","alt"],ht={class:"property-info"},yt={class:"info-top"},_t={class:"title"},wt={class:"title-status-tag"},Tt={class:"title-text"},Ct={class:"info-price"},kt={class:"price-integer"},Nt={class:"price-decimal"},bt={class:"parameters"},It={class:"location"},xt={class:"tabs-section"},Dt={key:0,class:"tabs-header-placeholder"},Rt={class:"user-info-content"},Mt={class:"company-info"},St={class:"company-header"},Pt={class:"company-logo"},Ht=["src","alt"],Et={class:"company-details"},Lt={class:"company-details-container"},Bt={class:"company-title"},Ut={class:"company-name"},Yt={class:"company-type"},$t={class:"follow-btn"},qt={class:"company-rating"},Wt={class:"rating"},jt={class:"rating-score"},At={class:"rating"},Vt={class:"rating-score"},Xt={class:"company-description"},Ot={class:"company-icon"},Ft={class:"section-content"},zt={class:"section-content"},Gt={class:"section-content"},Kt={class:"recommend-section"},Jt={class:"section-title"},Qt={class:"title-container"},Zt={class:"recommend-content"},es={class:"property-cards"},f=100,ts=ze({__name:"PropertyDetail",setup(ss){const _=Ae(),M=Ve(),S=i(0),p=i(),P=i(!1),H=i(!1),b=i(null),h=i(!1),E=i(!1),Y=i(!1),w=i("userInfo"),T=i(!1),ee=i(0),$=i(null),q=i({}),y=i(),pe=i(),W=i(),j=i(),A=i(),V=i(),de=i(),me=i(),ve=i(),I=i(!1),C=i({x:0,y:0,translateX:0,translateY:0}),te=s=>{s==="自由交易"?M.push({name:"freedomHome"}):M.back()},se=i(!1),o=i({productId:"",productName:"某大型企业废旧物资一批——废旧变压器共计11台",location:"内蒙古自治区乌兰察布市商都县某某地址某某地址",quantity:"11",weight:"3.6",power:"3.6kW~10kW",currentPrice:6e4,viewCount:0,status:"ongoing",enterpriseLogo:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",enterpriseName:"天津水泥集团有限公司",enterpriseType:"国企",peopleCount:"1234567890",rating:9.5,companyDescription:'天津水泥集团有限公司成立于1998年，是一家专业从事水泥生产的大型企业。公司位于天津市，占地面积约500亩，拥有员工1000余人。公司主要生产各种规格的水泥产品，年产能达到500万吨。公司秉承"质量第一，客户至上"的经营理念，致力于为客户提供优质的产品和服务。',images:["https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png"]}),ge=async()=>{const s=_.query.id,t=_.query.type;if(!s){J.error("缺少资产ID参数");return}try{se.value=!0;let n;if(t==="4"||t==="5"?n=await U.getSupplyDemandDetail(s):t==="2"?n=await U.getAssetEntrustDetail(s):n=await U.getSupplyDemandDetail(s),n.success||n.code===200){const a=n.result;if(a){const l=`${a.province||""}${a.city||""}${a.district||""}${a.address||""}`;let r=o.value.images;a.attachmentList&&Array.isArray(a.attachmentList)&&a.attachmentList.length>0&&(r=a.attachmentList.map(c=>c.url||c)),o.value={productId:a.id||s,productName:a.infoTitle||o.value.productName,location:l||o.value.location,quantity:a.quantity?a.quantity.toString():o.value.quantity,weight:a.weight?a.weight.toString():o.value.weight,power:a.power||o.value.power,currentPrice:a.price||o.value.currentPrice,viewCount:a.viewNum,status:a.status===6?"completed":a.type==="5"?"purchase":"supply",enterpriseLogo:a.enterpriseLogo||o.value.enterpriseLogo,enterpriseName:a.enterpriseName||a.createBy||o.value.enterpriseName,enterpriseType:a.enterpriseType||o.value.enterpriseType,peopleCount:a.peopleCount||o.value.peopleCount,rating:a.rating||o.value.rating,companyDescription:a.materialDesc||a.highlights||o.value.companyDescription,images:r},a.type==="5"?o.value.status="ongoing":a.type==="4"&&(o.value.status="supply")}}else J.error(n.message||n.msg||"获取资产详情失败")}catch(n){console.error("获取资产详情失败:",n),J.error("获取资产详情失败，请稍后重试")}finally{se.value=!1}},fe=i([{productId:"3",productName:"200钢栈桥",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250710/17521325169594.png",currentPrice:1700,priceUnit:"元/吨",statusName:"待发布",viewCount:152,enterpriseLogo:"",enterpriseName:"湖北省宜昌市某企业",enterpriseType:"私企",status:"upcoming",productCount:1,productCountUnit:"批",productWeight:2,productWeightUnit:"吨"},{productId:"3",productName:"闲置铝芯电缆一批",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250522/17478958852404.png",currentPrice:5e3,priceUnit:"元/吨",statusName:"待发布",viewCount:600,enterpriseLogo:"",enterpriseName:"河南省新乡市某机械厂",enterpriseType:"央企",status:"upcoming",productCount:1,productCountUnit:"批",productWeight:5,productWeightUnit:"吨"},{productId:"3",productName:"废旧工程车与废旧设备一批",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250707/17518775754193.png",currentPrice:1600,priceUnit:"元/吨",statusName:"待发布",viewCount:572,enterpriseLogo:"",enterpriseName:"辽宁省大连市某大型企业",enterpriseType:"民企",status:"ongoing",productCount:1,productCountUnit:"批",productWeight:3,productWeightUnit:"吨"},{productId:"3",productName:"废旧电动叉车一台",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250715/17525743322350.png",currentPrice:5e3,priceUnit:"元/台",statusName:"待发布",viewCount:3500,enterpriseLogo:"",enterpriseName:"山东聊城某企业",enterpriseType:"私企",status:"upcoming",productCount:1,productCountUnit:"台",productWeight:1,productWeightUnit:"吨",productPower:36.8,productPowerUnit:"kW"}]),he=le(()=>Math.floor(o.value.currentPrice)),ye=le(()=>((o.value.currentPrice-Math.floor(o.value.currentPrice))*100).toFixed(0).padStart(2,"0")),_e=s=>{S.value=s},we=()=>{E.value=!E.value},Te=()=>{Y.value=!Y.value},X=i(!1),Ce=()=>{X.value=!0},k=()=>{if(!p.value)return;const s=p.value;P.value=s.scrollTop>0,H.value=s.scrollTop<s.scrollHeight-s.clientHeight},ke=()=>{p.value&&(p.value.scrollBy({top:-120,behavior:"smooth"}),setTimeout(k,300))},Ne=()=>{p.value&&(p.value.scrollBy({top:120,behavior:"smooth"}),setTimeout(k,300))},be=()=>{!p.value||h.value||!P.value||(h.value=!0,p.value.scrollBy({top:-120,behavior:"smooth"}),setTimeout(()=>{k(),h.value=!1},400))},Ie=()=>{if(!p.value||h.value||!H.value)return;h.value=!0,p.value.scrollBy({top:120,behavior:"smooth"}),setTimeout(()=>{k(),h.value=!1},400)},xe=s=>{if(!p.value)return{isTopEdge:!1,isBottomEdge:!1};const n=p.value.getBoundingClientRect(),a=n.top,l=80,r=s-a,c=n.height;return{isTopEdge:r<=l&&P.value,isBottomEdge:r>=c-l&&H.value}},O=()=>{b.value&&(clearTimeout(b.value),b.value=null),h.value=!1},De=s=>{O();const{isTopEdge:t,isBottomEdge:n}=xe(s.clientY);t?b.value=window.setTimeout(()=>{be()},100):n&&(b.value=window.setTimeout(()=>{Ie()},100))},Re=()=>{O()},Me=s=>{M.push({name:"propertyDetail",query:{id:s.productId,productName:s.productName}})},L=s=>{w.value=s;const n={userInfo:W.value,assetHighlights:j.value,productParams:A.value,detailIntro:V.value}[s];if(n&&y.value){const a=y.value.offsetHeight,l=n.getBoundingClientRect(),r=window.pageYOffset||document.documentElement.scrollTop,x=l.top+r-a-120;window.scrollTo({top:x,behavior:"smooth"})}};function oe(){if(T.value&&$.value){const s=$.value.getBoundingClientRect();q.value={position:"fixed",top:"92px",left:s.left+"px",width:s.width-1+"px",zIndex:"99",background:"#fff"}}else q.value={}}const ae=()=>{if(!y.value)return;const s=window.scrollY||document.documentElement.scrollTop;s>=ee.value-120?(T.value=!0,oe()):(T.value=!1,oe());const t=y.value.offsetHeight,n=T.value?t:0,a=s+t+n+50,l=[{id:"userInfo",element:W.value},{id:"assetHighlights",element:j.value},{id:"productParams",element:A.value},{id:"detailIntro",element:V.value}];for(let r=l.length-1;r>=0;r--){const c=l[r];if(c.element){const x=c.element.getBoundingClientRect(),F=window.pageYOffset||document.documentElement.scrollTop,z=x.top+F;if(a>=z-100){w.value=c.id;break}}}},Se=()=>{I.value=!0},Pe=()=>{I.value=!1,C.value={x:0,y:0,translateX:0,translateY:0}},He=s=>{if(!I.value)return;const n=s.currentTarget.getBoundingClientRect();let a=s.clientX-n.left,l=s.clientY-n.top,r=a-f/2,c=l-f/2;r=Math.max(0,r),c=Math.max(0,c);const x=n.width-f,F=n.height-f;r=Math.min(r,x),c=Math.min(c,F);const z=520,Be=520,ne=520,ie=520,B=2.5,Ue=r+f/2,Ye=c+f/2;let G=Ue*B-ne/2,K=Ye*B-ie/2;const $e=z*B-ne,qe=Be*B-ie;G=Math.max(0,Math.min(G,$e)),K=Math.max(0,Math.min(K,qe)),C.value={x:r,y:c,translateX:G,translateY:K}},Ee=s=>{M.push({name:"enterpriseDetail",query:{id:s,productName:o.value.productName}})},Le=async()=>{const s=_.query.id,t=o.value.viewCount||0;if(s)try{const n=await U.addViewCount(s,t);n.success||n.code}catch(n){console.error("增加围观数量请求失败:",n)}};return Ge(async()=>{const s=_.params.id;if(o.value.productId=s,await ge(),await Le(),await Ke(),k(),y.value){const t=y.value.getBoundingClientRect(),n=window.pageYOffset||document.documentElement.scrollTop;ee.value=t.top+n}p.value&&p.value.addEventListener("scroll",k),window.addEventListener("scroll",ae)}),Je(()=>{window.removeEventListener("scroll",ae),O()}),(s,t)=>{const n=je,a=We;return d(),m("div",et,[e("div",{class:"detail-section",ref_key:"containerRef",ref:$},[e("div",tt,[e("div",st,[re(_).query.crumbsTitle!=="自由交易"?(d(),m(D,{key:0},[e("span",{class:"free-title",onClick:t[0]||(t[0]=l=>te("自由交易"))},"自由交易"),t[8]||(t[8]=e("span",{class:"separator"},">",-1))],64)):(d(),m(D,{key:1},[e("span",{class:"free-title",onClick:t[1]||(t[1]=l=>te("other"))},u(re(_).query.crumbsTitle),1),t[9]||(t[9]=e("span",{class:"separator"},">",-1))],64)),e("span",ot,u(o.value.productName),1)]),e("div",at,[e("div",{class:"collect",onClick:we},[v(g,{iconName:E.value?"freedom-propertyDetail-checked-star":"freedom-propertyDetail-star",className:"collect-icon"},null,8,["iconName"]),e("span",nt,u(E.value?"已收藏":"收藏"),1)]),e("div",it,[v(g,{iconName:"freedom-propertyDetail-eye",className:"eye-icon"}),e("span",null,u(o.value.viewCount),1)])])]),e("div",lt,[e("div",rt,[P.value?(d(),m("div",{key:0,class:"scroll-arrow scroll-up",onClick:ke},[t[10]||(t[10]=e("div",{class:"arrow-overlay"},null,-1)),v(g,{iconName:"freedom-propertyDetail-down",className:"scroll-icon scroll-icon-up"})])):R("",!0),e("div",{class:"image-list",ref_key:"imageListRef",ref:p,onMousemove:De,onMouseleave:Re},[(d(!0),m(D,null,Q(o.value.images,(l,r)=>(d(),m("div",{key:r,class:N(["image-item",{active:S.value===r}]),onMousemove:c=>_e(r)},[e("img",{src:l,alt:`图片${r+1}`},null,8,ut)],42,ct))),128))],544),H.value?(d(),m("div",{key:1,class:"scroll-arrow scroll-down",onClick:Ne},[t[11]||(t[11]=e("div",{class:"arrow-overlay"},null,-1)),v(g,{iconName:"freedom-propertyDetail-down",className:"scroll-icon"})])):R("",!0)]),e("div",pt,[e("div",dt,[e("div",mt,[e("img",{src:o.value.images[S.value],alt:o.value.productName,onMouseenter:Se,onMouseleave:Pe,onMousemove:He},null,40,vt),I.value?(d(),m("div",{key:0,class:"magnifier-mask",style:Z({left:C.value.x+"px",top:C.value.y+"px",width:f+"px",height:f+"px"})},null,4)):R("",!0)])]),I.value?(d(),m("div",gt,[e("img",{src:o.value.images[S.value],alt:o.value.productName,style:Z({transform:`translate(-${C.value.translateX||0}px, -${C.value.translateY||0}px)`})},null,12,ft)])):R("",!0)]),e("div",ht,[e("div",yt,[e("div",_t,[e("div",null,[e("span",wt,u(o.value.status==="ongoing"?"求购":"供应"),1),e("span",Tt,u(o.value.productName),1),e("span",{class:"detail-btn",onClick:Ce},[t[12]||(t[12]=ce(" 查看详情 ")),v(g,{iconName:"auction-arrows-right",className:"detail-icon"})])])]),e("div",Ct,[t[13]||(t[13]=e("span",{class:"currency"},"￥",-1)),e("span",kt,u(he.value),1),e("span",Nt,"."+u(ye.value),1),t[14]||(t[14]=e("span",{class:"price-unit"},"元/吨",-1))]),e("div",bt,[e("span",null,u(o.value.quantity)+"台",1),e("span",null,u(o.value.weight)+"吨",1),e("span",null,u(o.value.power),1)]),(d(),m(D,null,Q(8,l=>e("div",It,[v(g,{iconName:"freedom-propertyDetail-location",className:"location-icon"}),e("span",null,u(o.value.location),1)])),64))])])]),e("div",xt,[e("div",{class:N(["tabs-header",{"tabs-header-fixed":T.value}]),ref_key:"tabsHeaderRef",ref:y,style:Z(q.value)},[e("div",{class:N(["tab-item",{active:w.value==="userInfo"}]),onClick:t[2]||(t[2]=l=>L("userInfo"))},t[15]||(t[15]=[e("span",null,"用户信息",-1)]),2),e("div",{class:N(["tab-item",{active:w.value==="assetHighlights"}]),onClick:t[3]||(t[3]=l=>L("assetHighlights"))},t[16]||(t[16]=[e("span",null,"资产亮点",-1)]),2),e("div",{class:N(["tab-item",{active:w.value==="productParams"}]),onClick:t[4]||(t[4]=l=>L("productParams"))},t[17]||(t[17]=[e("span",null,"产品参数",-1)]),2),e("div",{class:N(["tab-item",{active:w.value==="detailIntro"}]),onClick:t[5]||(t[5]=l=>L("detailIntro"))},t[18]||(t[18]=[e("span",null,"详细介绍",-1)]),2)],6),T.value?(d(),m("div",Dt)):R("",!0),e("div",{class:"tabs-content",ref_key:"tabsContentRef",ref:pe},[e("div",{class:"content-section userinfo",id:"userInfo",ref_key:"userInfoRef",ref:W,onClick:t[6]||(t[6]=l=>Ee("1"))},[e("div",Rt,[e("div",Mt,[e("div",St,[e("div",Pt,[e("img",{src:o.value.enterpriseLogo,alt:o.value.enterpriseName},null,8,Ht)]),e("div",Et,[e("div",Lt,[e("div",Bt,[e("span",Ut,u(o.value.enterpriseName),1),e("div",Yt,[v(Oe,{enterpriseType:o.value.enterpriseType},null,8,["enterpriseType"])]),e("div",$t,[v(n,{type:"primary",onClick:Te},{default:ue(()=>[ce(" + "+u(Y.value?"已关注":"关注"),1)]),_:1})])]),e("div",qt,[e("div",Wt,[e("span",jt,u(o.value.peopleCount),1),t[19]||(t[19]=e("span",{class:"rating-text"},"账号",-1))]),e("div",At,[e("span",Vt,u(o.value.rating),1),t[20]||(t[20]=e("span",{class:"rating-text"},"万粉丝",-1))])])]),e("div",Xt,u(o.value.companyDescription),1)]),e("div",Ot,[v(g,{iconName:"freedom-propertyDetail-down",className:"show-company-icon"})])])])])],512),e("div",{class:"content-section",id:"assetHighlights",ref_key:"assetHighlightsRef",ref:j,style:{"margin-top":"40px"}},[e("div",Ft,[e("span",{class:"section-title",ref_key:"assetHighlightsTitleRef",ref:de}," 资产亮点 ",512),t[21]||(t[21]=e("div",{class:"section-body"},[e("p",null,"资产亮点内容区域，待完善...")],-1))])],512),e("div",{class:"content-section",id:"productParams",ref_key:"productParamsRef",ref:A},[e("div",zt,[e("span",{class:"section-title",ref_key:"productParamsTitleRef",ref:me}," 产品参数 ",512),t[22]||(t[22]=e("div",{class:"section-body"},[e("p",null,"产品参数内容区域，待完善...")],-1))])],512),e("div",{class:"content-section",id:"detailIntro",ref_key:"detailIntroRef",ref:V},[e("div",Gt,[e("span",{class:"section-title",ref_key:"detailIntroTitleRef",ref:ve},"详细介绍",512),t[23]||(t[23]=e("div",{class:"section-body"},[e("p",null,"详细介绍内容区域，待完善...")],-1))])],512)],512)])],512),e("div",Kt,[e("div",Jt,[e("div",Qt,[v(g,{iconName:"freedom-propertyDetail-recommended-assets",className:"title-icon"}),t[24]||(t[24]=e("span",null,"推荐资产",-1))])]),e("div",Zt,[e("div",es,[(d(!0),m(D,null,Q(fe.value.slice(0,4),l=>(d(),Qe(Fe,Ze({key:l.productId},{ref_for:!0},l,{onClick:Me}),null,16))),128))])])]),v(a,{modelValue:X.value,"onUpdate:modelValue":t[7]||(t[7]=l=>X.value=l),title:"查看详情","confirm-button-text":"缴纳保证金"},{default:ue(()=>t[25]||(t[25]=[e("div",{class:"detail-content"}," 查看该资产详情，客户信息需要缴纳一定数额的保证金，请缴纳保证金后继续查看 ",-1)])),_:1,__:[25]},8,["modelValue"])])}}}),Ts=Xe(ts,[["__scopeId","data-v-c4cdf06a"]]);export{Ts as default};
