import{u as e,b as o}from"./element-plus-BiAL0NdQ.js";import{S as l,_ as t}from"./index-CCmno-0X.js";import{d as a,r as s,t as n,E as c,ab as d,G as i,b as u,n as m,C as p,K as r,I as f,o as y,$ as B}from"./vue-vendor-D6tHD5lA.js";const v={class:"modal-header"},k={class:"modal-title"},h={class:"title-text"},C={class:"modal-content"},b={class:"modal-footer"},w=t(a({__name:"Modal",props:{modelValue:{type:Boolean},title:{default:"提示"},titleIcon:{},width:{default:"500px"},top:{default:"15vh"},modal:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},destroyOnClose:{type:Boolean,default:!1},appendToBody:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},customClass:{default:""},showFooter:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},cancelButtonText:{default:"取消"},confirmButtonText:{default:"确定"},confirmLoading:{type:Boolean,default:!1},buttonSize:{default:"default"}},emits:["update:modelValue","confirm","cancel","close","open","opened","closed"],setup(t,{emit:a}){const w=t,x=a,V=s(w.modelValue);n(()=>w.modelValue,e=>{V.value=e,e&&x("open")},{immediate:!0}),n(V,e=>{x("update:modelValue",e),x(e?"opened":"closed")});const _=()=>{V.value=!1,x("close")},z=()=>{x("confirm")},O=()=>{V.value=!1,x("cancel")};return(t,a)=>{const s=o,n=e;return y(),c(n,{modelValue:V.value,"onUpdate:modelValue":a[0]||(a[0]=e=>V.value=e),title:t.title,width:t.width,top:t.top,modal:t.modal,"close-on-click-modal":t.closeOnClickModal,"close-on-press-escape":t.closeOnPressEscape,"show-close":!1,"before-close":_,"destroy-on-close":t.destroyOnClose,"append-to-body":t.appendToBody,"lock-scroll":t.lockScroll,"custom-class":t.customClass,class:"custom-modal"},d({header:i(()=>[u("div",v,[u("div",k,[t.titleIcon?(y(),c(l,{key:0,iconName:t.titleIcon,className:"title-icon"},null,8,["iconName"])):p("",!0)]),u("span",h,r(t.title),1),u("div",{class:"modal-close",onClick:_},[f(l,{iconName:"model-close",className:"close-icon"})])])]),default:i(()=>[u("div",C,[m(t.$slots,"default",{},void 0,!0)])]),_:2},[t.showFooter?{name:"footer",fn:i(()=>[u("div",b,[m(t.$slots,"footer",{},()=>[t.showCancelButton?(y(),c(s,{key:0,onClick:O,size:t.buttonSize,class:"cancel-btn"},{default:i(()=>[B(r(t.cancelButtonText),1)]),_:1},8,["size"])):p("",!0),t.showConfirmButton?(y(),c(s,{key:1,type:"primary",onClick:z,loading:t.confirmLoading,size:t.buttonSize},{default:i(()=>[B(r(t.confirmButtonText),1)]),_:1},8,["loading","size"])):p("",!0)],!0)])]),key:"0"}:void 0]),1032,["modelValue","title","width","top","modal","close-on-click-modal","close-on-press-escape","destroy-on-close","append-to-body","lock-scroll","custom-class"])}}}),[["__scopeId","data-v-fcbb7871"]]);export{w as M};
