import{q as n,r as _}from"./element-plus-DZBbDeaO.js";import{d as i,E as a,G as o,a as u,a1 as m,b as s,K as p,F as d,o as t}from"./vue-vendor-2E6AJATX.js";import{_ as h}from"./index-CkIekciI.js";const f={class:"carousel-item"},C=["src","alt"],g={class:"carousel-content"},v={class:"carousel-title"},k=i({__name:"Carousel",props:{items:{}},setup(x){return(r,B)=>{const l=_,c=n;return t(),a(c,{height:"340px",interval:5e3,arrow:"never",autoplay:!0},{default:o(()=>[(t(!0),u(d,null,m(r.items,e=>(t(),a(l,{key:e.title},{default:o(()=>[s("div",f,[s("img",{src:e.image,alt:e.title,class:"carousel-image"},null,8,C),s("div",g,[s("h2",v,p(e.title),1)])])]),_:2},1024))),128))]),_:1})}}}),F=h(k,[["__scopeId","data-v-4705a81c"]]);export{F as C};
