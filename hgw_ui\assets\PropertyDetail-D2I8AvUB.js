import{M as e}from"./Modal-DCQzYcok.js";import{b as t,E as a}from"./element-plus-BiAL0NdQ.js";import{a as s,u as i}from"./vue-router-C0lzQS1p.js";import{S as o,_ as l}from"./index-CCmno-0X.js";import{C as n,P as r}from"./PropertyCard-BPAT2wDM.js";import{n as c}from"./utils-common-PdkFOSu3.js";import{d as u,r as p,c as d,s as m,z as v,x as g,a as y,b as h,I as f,u as w,F as N,K as C,C as b,a1 as k,L as T,$ as I,D as j,G as x,E as D,q as P,o as _}from"./vue-vendor-D6tHD5lA.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./app-stores-CLUCXxRF.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";const M={class:"property-detail"},L={class:"header-section"},E={class:"breadcrumb"},R={class:"current"},q={class:"header-right"},B={class:"collect-text"},U={class:"view-count"},H={class:"content-section"},W={class:"image-list-container"},Y=["onMousemove"],$=["src","alt"],S={class:"image-preview"},X={class:"preview-container"},V={class:"image-container"},A=["src","alt"],O={key:0,class:"magnified-view"},z=["src","alt"],F={class:"property-info"},G={class:"info-top"},K={class:"title"},J={class:"title-status-tag"},Q={class:"title-text"},Z={class:"info-price"},ee={class:"price-integer"},te={class:"price-decimal"},ae={class:"parameters"},se={class:"location"},ie={class:"tabs-section"},oe={key:0,class:"tabs-header-placeholder"},le={class:"user-info-content"},ne={class:"company-info"},re={class:"company-header"},ce={class:"company-logo"},ue=["src","alt"],pe={class:"company-details"},de={class:"company-details-container"},me={class:"company-title"},ve={class:"company-name"},ge={class:"company-type"},ye={class:"follow-btn"},he={class:"company-rating"},fe={class:"rating"},we={class:"rating-score"},Ne={class:"rating"},Ce={class:"rating-score"},be={class:"company-description"},ke={class:"company-icon"},Te={class:"section-content"},Ie={class:"section-content"},je={class:"section-content"},xe={class:"recommend-section"},De={class:"section-title"},Pe={class:"title-container"},_e={class:"recommend-content"},Me={class:"property-cards"},Le=100,Ee=l(u({__name:"PropertyDetail",setup(l){const u=s(),Ee=i(),Re=p(0),qe=p(),Be=p(!1),Ue=p(!1),He=p(null),We=p(!1),Ye=p(!1),$e=p(!1),Se=p("userInfo"),Xe=p(!1),Ve=p(0),Ae=p(null),Oe=p({}),ze=p(),Fe=p(),Ge=p(),Ke=p(),Je=p(),Qe=p(),Ze=p(),et=p(),tt=p(),at=p(!1),st=p({x:0,y:0,translateX:0,translateY:0}),it=e=>{"自由交易"===e?Ee.push({name:"freedomHome"}):Ee.back()},ot=p(!1),lt=p({productId:"",productName:"某大型企业废旧物资一批——废旧变压器共计11台",location:"内蒙古自治区乌兰察布市商都县某某地址某某地址",quantity:"11",weight:"3.6",power:"3.6kW~10kW",currentPrice:6e4,viewCount:0,status:"ongoing",enterpriseLogo:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",enterpriseName:"天津水泥集团有限公司",enterpriseType:"国企",peopleCount:"1234567890",rating:9.5,companyDescription:'天津水泥集团有限公司成立于1998年，是一家专业从事水泥生产的大型企业。公司位于天津市，占地面积约500亩，拥有员工1000余人。公司主要生产各种规格的水泥产品，年产能达到500万吨。公司秉承"质量第一，客户至上"的经营理念，致力于为客户提供优质的产品和服务。',images:["https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png","https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png"]}),nt=p([{productId:"3",productName:"200钢栈桥",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250710/17521325169594.png",currentPrice:1700,priceUnit:"元/吨",statusName:"待发布",viewCount:152,enterpriseLogo:"",enterpriseName:"湖北省宜昌市某企业",enterpriseType:"私企",status:"upcoming",productCount:1,productCountUnit:"批",productWeight:2,productWeightUnit:"吨"},{productId:"3",productName:"闲置铝芯电缆一批",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250522/17478958852404.png",currentPrice:5e3,priceUnit:"元/吨",statusName:"待发布",viewCount:600,enterpriseLogo:"",enterpriseName:"河南省新乡市某机械厂",enterpriseType:"央企",status:"upcoming",productCount:1,productCountUnit:"批",productWeight:5,productWeightUnit:"吨"},{productId:"3",productName:"废旧工程车与废旧设备一批",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250707/17518775754193.png",currentPrice:1600,priceUnit:"元/吨",statusName:"待发布",viewCount:572,enterpriseLogo:"",enterpriseName:"辽宁省大连市某大型企业",enterpriseType:"民企",status:"ongoing",productCount:1,productCountUnit:"批",productWeight:3,productWeightUnit:"吨"},{productId:"3",productName:"废旧电动叉车一台",productImage:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250715/17525743322350.png",currentPrice:5e3,priceUnit:"元/台",statusName:"待发布",viewCount:3500,enterpriseLogo:"",enterpriseName:"山东聊城某企业",enterpriseType:"私企",status:"upcoming",productCount:1,productCountUnit:"台",productWeight:1,productWeightUnit:"吨",productPower:36.8,productPowerUnit:"kW"}]),rt=d(()=>Math.floor(lt.value.currentPrice)),ct=d(()=>(100*(lt.value.currentPrice-Math.floor(lt.value.currentPrice))).toFixed(0).padStart(2,"0")),ut=()=>{Ye.value=!Ye.value},pt=()=>{$e.value=!$e.value},dt=p(!1),mt=()=>{dt.value=!0},vt=()=>{if(!qe.value)return;const e=qe.value;Be.value=e.scrollTop>0,Ue.value=e.scrollTop<e.scrollHeight-e.clientHeight},gt=()=>{qe.value&&(qe.value.scrollBy({top:-120,behavior:"smooth"}),setTimeout(vt,300))},yt=()=>{qe.value&&(qe.value.scrollBy({top:120,behavior:"smooth"}),setTimeout(vt,300))},ht=()=>{He.value&&(clearTimeout(He.value),He.value=null),We.value=!1},ft=e=>{ht();const{isTopEdge:t,isBottomEdge:a}=(e=>{if(!qe.value)return{isTopEdge:!1,isBottomEdge:!1};const t=qe.value.getBoundingClientRect(),a=e-t.top,s=t.height;return{isTopEdge:a<=80&&Be.value,isBottomEdge:a>=s-80&&Ue.value}})(e.clientY);t?He.value=window.setTimeout(()=>{qe.value&&!We.value&&Be.value&&(We.value=!0,qe.value.scrollBy({top:-120,behavior:"smooth"}),setTimeout(()=>{vt(),We.value=!1},400))},100):a&&(He.value=window.setTimeout(()=>{(()=>{if(!qe.value||We.value||!Ue.value)return;We.value=!0,qe.value.scrollBy({top:120,behavior:"smooth"}),setTimeout(()=>{vt(),We.value=!1},400)})()},100))},wt=()=>{ht()},Nt=e=>{Ee.push({name:"propertyDetail",query:{id:e.productId,productName:e.productName}})},Ct=e=>{Se.value=e;const t={userInfo:Ge.value,assetHighlights:Ke.value,productParams:Je.value,detailIntro:Qe.value}[e];if(t&&ze.value){const e=ze.value.offsetHeight,a=t.getBoundingClientRect(),s=window.pageYOffset||document.documentElement.scrollTop,i=a.top+s-e-120;window.scrollTo({top:i,behavior:"smooth"})}};function bt(){if(Xe.value&&Ae.value){const e=Ae.value.getBoundingClientRect();Oe.value={position:"fixed",top:"92px",left:e.left+"px",width:e.width-1+"px",zIndex:"99",background:"#fff"}}else Oe.value={}}const kt=()=>{if(!ze.value)return;const e=window.scrollY||document.documentElement.scrollTop;e>=Ve.value-120?(Xe.value=!0,bt()):(Xe.value=!1,bt());const t=ze.value.offsetHeight,a=e+t+(Xe.value?t:0)+50,s=[{id:"userInfo",element:Ge.value},{id:"assetHighlights",element:Ke.value},{id:"productParams",element:Je.value},{id:"detailIntro",element:Qe.value}];for(let i=s.length-1;i>=0;i--){const e=s[i];if(e.element){const t=e.element.getBoundingClientRect(),s=window.pageYOffset||document.documentElement.scrollTop;if(a>=t.top+s-100){Se.value=e.id;break}}}},Tt=()=>{at.value=!0},It=()=>{at.value=!1,st.value={x:0,y:0,translateX:0,translateY:0}},jt=e=>{if(!at.value)return;const t=e.currentTarget.getBoundingClientRect();let a=e.clientX-t.left-50,s=e.clientY-t.top-50;a=Math.max(0,a),s=Math.max(0,s);const i=t.width-Le,o=t.height-Le;a=Math.min(a,i),s=Math.min(s,o);const l=2.5;let n=(a+50)*l-260,r=(s+50)*l-260;n=Math.max(0,Math.min(n,780)),r=Math.max(0,Math.min(r,780)),st.value={x:a,y:s,translateX:n,translateY:r}};return m(async()=>{const e=u.params.id;if(lt.value.productId=e,await(async()=>{const e=u.query.id,t=u.query.type;if(e)try{let s;if(ot.value=!0,s="4"===t||"5"===t?await c.getSupplyDemandDetail(e):"2"===t?await c.getAssetEntrustDetail(e):await c.getSupplyDemandDetail(e),s.success||200===s.code){const t=s.result;if(t){const a=`${t.province||""}${t.city||""}${t.district||""}${t.address||""}`;let s=lt.value.images;t.attachmentList&&Array.isArray(t.attachmentList)&&t.attachmentList.length>0&&(s=t.attachmentList.map(e=>e.url||e)),lt.value={productId:t.id||e,productName:t.infoTitle||lt.value.productName,location:a||lt.value.location,quantity:t.quantity?t.quantity.toString():lt.value.quantity,weight:t.weight?t.weight.toString():lt.value.weight,power:t.power||lt.value.power,currentPrice:t.price||lt.value.currentPrice,viewCount:t.viewNum,status:6===t.status?"completed":"5"===t.type?"purchase":"supply",enterpriseLogo:t.enterpriseLogo||lt.value.enterpriseLogo,enterpriseName:t.enterpriseName||t.createBy||lt.value.enterpriseName,enterpriseType:t.enterpriseType||lt.value.enterpriseType,peopleCount:t.peopleCount||lt.value.peopleCount,rating:t.rating||lt.value.rating,companyDescription:t.materialDesc||t.highlights||lt.value.companyDescription,images:s},"5"===t.type?lt.value.status="ongoing":"4"===t.type&&(lt.value.status="supply")}}else a.error(s.message||s.msg||"获取资产详情失败")}catch(s){a.error("获取资产详情失败，请稍后重试")}finally{ot.value=!1}else a.error("缺少资产ID参数")})(),await(async()=>{const e=u.query.id,t=lt.value.viewCount||0;if(e)try{const a=await c.addViewCount(e,t);a.success||a.code}catch(a){}})(),await v(),vt(),ze.value){const e=ze.value.getBoundingClientRect(),t=window.pageYOffset||document.documentElement.scrollTop;Ve.value=e.top+t}qe.value&&qe.value.addEventListener("scroll",vt),window.addEventListener("scroll",kt)}),g(()=>{window.removeEventListener("scroll",kt),ht()}),(a,s)=>{const i=t,l=e;return _(),y("div",M,[h("div",{class:"detail-section",ref_key:"containerRef",ref:Ae},[h("div",L,[h("div",E,["自由交易"!==w(u).query.crumbsTitle?(_(),y(N,{key:0},[h("span",{class:"free-title",onClick:s[0]||(s[0]=e=>it("自由交易"))},"自由交易"),s[8]||(s[8]=h("span",{class:"separator"},">",-1))],64)):(_(),y(N,{key:1},[h("span",{class:"free-title",onClick:s[1]||(s[1]=e=>it("other"))},C(w(u).query.crumbsTitle),1),s[9]||(s[9]=h("span",{class:"separator"},">",-1))],64)),h("span",R,C(lt.value.productName),1)]),h("div",q,[h("div",{class:"collect",onClick:ut},[f(o,{iconName:Ye.value?"freedom-propertyDetail-checked-star":"freedom-propertyDetail-star",className:"collect-icon"},null,8,["iconName"]),h("span",B,C(Ye.value?"已收藏":"收藏"),1)]),h("div",U,[f(o,{iconName:"freedom-propertyDetail-eye",className:"eye-icon"}),h("span",null,C(lt.value.viewCount),1)])])]),h("div",H,[h("div",W,[Be.value?(_(),y("div",{key:0,class:"scroll-arrow scroll-up",onClick:gt},[s[10]||(s[10]=h("div",{class:"arrow-overlay"},null,-1)),f(o,{iconName:"freedom-propertyDetail-down",className:"scroll-icon scroll-icon-up"})])):b("",!0),h("div",{class:"image-list",ref_key:"imageListRef",ref:qe,onMousemove:ft,onMouseleave:wt},[(_(!0),y(N,null,k(lt.value.images,(e,t)=>(_(),y("div",{key:t,class:j(["image-item",{active:Re.value===t}]),onMousemove:e=>(e=>{Re.value=e})(t)},[h("img",{src:e,alt:`图片${t+1}`},null,8,$)],42,Y))),128))],544),Ue.value?(_(),y("div",{key:1,class:"scroll-arrow scroll-down",onClick:yt},[s[11]||(s[11]=h("div",{class:"arrow-overlay"},null,-1)),f(o,{iconName:"freedom-propertyDetail-down",className:"scroll-icon"})])):b("",!0)]),h("div",S,[h("div",X,[h("div",V,[h("img",{src:lt.value.images[Re.value],alt:lt.value.productName,onMouseenter:Tt,onMouseleave:It,onMousemove:jt},null,40,A),at.value?(_(),y("div",{key:0,class:"magnifier-mask",style:T({left:st.value.x+"px",top:st.value.y+"px",width:Le+"px",height:Le+"px"})},null,4)):b("",!0)])]),at.value?(_(),y("div",O,[h("img",{src:lt.value.images[Re.value],alt:lt.value.productName,style:T({transform:`translate(-${st.value.translateX||0}px, -${st.value.translateY||0}px)`})},null,12,z)])):b("",!0)]),h("div",F,[h("div",G,[h("div",K,[h("div",null,[h("span",J,C("ongoing"===lt.value.status?"求购":"供应"),1),h("span",Q,C(lt.value.productName),1),h("span",{class:"detail-btn",onClick:mt},[s[12]||(s[12]=I(" 查看详情 ")),f(o,{iconName:"auction-arrows-right",className:"detail-icon"})])])]),h("div",Z,[s[13]||(s[13]=h("span",{class:"currency"},"￥",-1)),h("span",ee,C(rt.value),1),h("span",te,"."+C(ct.value),1),s[14]||(s[14]=h("span",{class:"price-unit"},"元/吨",-1))]),h("div",ae,[h("span",null,C(lt.value.quantity)+"台",1),h("span",null,C(lt.value.weight)+"吨",1),h("span",null,C(lt.value.power),1)]),(_(),y(N,null,k(8,e=>h("div",se,[f(o,{iconName:"freedom-propertyDetail-location",className:"location-icon"}),h("span",null,C(lt.value.location),1)])),64))])])]),h("div",ie,[h("div",{class:j(["tabs-header",{"tabs-header-fixed":Xe.value}]),ref_key:"tabsHeaderRef",ref:ze,style:T(Oe.value)},[h("div",{class:j(["tab-item",{active:"userInfo"===Se.value}]),onClick:s[2]||(s[2]=e=>Ct("userInfo"))},s[15]||(s[15]=[h("span",null,"用户信息",-1)]),2),h("div",{class:j(["tab-item",{active:"assetHighlights"===Se.value}]),onClick:s[3]||(s[3]=e=>Ct("assetHighlights"))},s[16]||(s[16]=[h("span",null,"资产亮点",-1)]),2),h("div",{class:j(["tab-item",{active:"productParams"===Se.value}]),onClick:s[4]||(s[4]=e=>Ct("productParams"))},s[17]||(s[17]=[h("span",null,"产品参数",-1)]),2),h("div",{class:j(["tab-item",{active:"detailIntro"===Se.value}]),onClick:s[5]||(s[5]=e=>Ct("detailIntro"))},s[18]||(s[18]=[h("span",null,"详细介绍",-1)]),2)],6),Xe.value?(_(),y("div",oe)):b("",!0),h("div",{class:"tabs-content",ref_key:"tabsContentRef",ref:Fe},[h("div",{class:"content-section userinfo",id:"userInfo",ref_key:"userInfoRef",ref:Ge,onClick:s[6]||(s[6]=e=>{return t="1",void Ee.push({name:"enterpriseDetail",query:{id:t,productName:lt.value.productName}});var t})},[h("div",le,[h("div",ne,[h("div",re,[h("div",ce,[h("img",{src:lt.value.enterpriseLogo,alt:lt.value.enterpriseName},null,8,ue)]),h("div",pe,[h("div",de,[h("div",me,[h("span",ve,C(lt.value.enterpriseName),1),h("div",ge,[f(n,{enterpriseType:lt.value.enterpriseType},null,8,["enterpriseType"])]),h("div",ye,[f(i,{type:"primary",onClick:pt},{default:x(()=>[I(" + "+C($e.value?"已关注":"关注"),1)]),_:1})])]),h("div",he,[h("div",fe,[h("span",we,C(lt.value.peopleCount),1),s[19]||(s[19]=h("span",{class:"rating-text"},"账号",-1))]),h("div",Ne,[h("span",Ce,C(lt.value.rating),1),s[20]||(s[20]=h("span",{class:"rating-text"},"万粉丝",-1))])])]),h("div",be,C(lt.value.companyDescription),1)]),h("div",ke,[f(o,{iconName:"freedom-propertyDetail-down",className:"show-company-icon"})])])])])],512),h("div",{class:"content-section",id:"assetHighlights",ref_key:"assetHighlightsRef",ref:Ke,style:{"margin-top":"40px"}},[h("div",Te,[h("span",{class:"section-title",ref_key:"assetHighlightsTitleRef",ref:Ze}," 资产亮点 ",512),s[21]||(s[21]=h("div",{class:"section-body"},[h("p",null,"资产亮点内容区域，待完善...")],-1))])],512),h("div",{class:"content-section",id:"productParams",ref_key:"productParamsRef",ref:Je},[h("div",Ie,[h("span",{class:"section-title",ref_key:"productParamsTitleRef",ref:et}," 产品参数 ",512),s[22]||(s[22]=h("div",{class:"section-body"},[h("p",null,"产品参数内容区域，待完善...")],-1))])],512),h("div",{class:"content-section",id:"detailIntro",ref_key:"detailIntroRef",ref:Qe},[h("div",je,[h("span",{class:"section-title",ref_key:"detailIntroTitleRef",ref:tt},"详细介绍",512),s[23]||(s[23]=h("div",{class:"section-body"},[h("p",null,"详细介绍内容区域，待完善...")],-1))])],512)],512)])],512),h("div",xe,[h("div",De,[h("div",Pe,[f(o,{iconName:"freedom-propertyDetail-recommended-assets",className:"title-icon"}),s[24]||(s[24]=h("span",null,"推荐资产",-1))])]),h("div",_e,[h("div",Me,[(_(!0),y(N,null,k(nt.value.slice(0,4),e=>(_(),D(r,P({key:e.productId},{ref_for:!0},e,{onClick:Nt}),null,16))),128))])])]),f(l,{modelValue:dt.value,"onUpdate:modelValue":s[7]||(s[7]=e=>dt.value=e),title:"查看详情","confirm-button-text":"缴纳保证金"},{default:x(()=>s[25]||(s[25]=[h("div",{class:"detail-content"}," 查看该资产详情，客户信息需要缴纳一定数额的保证金，请缴纳保证金后继续查看 ",-1)])),_:1,__:[25]},8,["modelValue"])])}}}),[["__scopeId","data-v-c4cdf06a"]]);export{Ee as default};
