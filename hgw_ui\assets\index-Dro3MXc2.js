import{d as x,r as f,Q as N,E as C,G as l,I as n,b as a,$ as S,o as w,c as B,a as q,ax as z,H as K,J as $}from"./vue-vendor-2E6AJATX.js";import{u as j,a as D}from"./vue-router-e9iWfNxP.js";import{_ as U,S as T}from"./index-CkIekciI.js";import{E as c,d as A,e as H,f as G,g as J,b as Q}from"./element-plus-DZBbDeaO.js";import{g as O,n as E,s as W,r as X}from"./utils-common-CvYGMv_l.js";import{a as Y}from"./app-stores-DSLz6__G.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";import"./element-icons-CT2GCbaF.js";import"./vendor-lodash-BPsl90Nm.js";const Z={class:"captcha-container"},ee=["src"],se=x({__name:"FreedomPasswordLogin",emits:["loginSuccess"],setup(R,{emit:p}){const b=p,_=Y(),u=f(!1),g=f(),e=N({username:"",password:"",captcha:"",rememberMe:!1}),t=f(""),v=f(""),I={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,message:"用户名长度不能少于2位",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"},{len:4,message:"验证码长度为4位",trigger:"blur"}]},P=async()=>{if(g.value)try{await g.value.validate(),u.value=!0;const o=await E.login({username:e.username,password:e.password,captcha:e.captcha,checkKey:v.value,loginSource:1});if(o.code===200){const s=o.result;_.freedomLogin(s),e.rememberMe?(localStorage.setItem("freedomRememberedUsername",e.username),W("freedomRememberedPassword",e.password)):(localStorage.removeItem("freedomRememberedUsername"),X("freedomRememberedPassword")),c.success("登录成功"),b("loginSuccess")}else h(),c.error(o.message||"登录失败，请检查用户名和密码")}catch(o){if(console.error("自由交易登录失败:",o),h(),o.response)switch(o.response.status){case 401:c.error("用户名、密码或验证码错误");break;case 403:c.error("账户被禁用，请联系管理员");break;case 500:c.error("服务器内部错误，请稍后重试");break;default:c.error("登录失败，请检查网络连接")}else c.error("登录失败，请检查用户名、密码和验证码")}finally{u.value=!1}},y=async()=>{try{const o=new Date().getTime(),s=Math.random().toString(36).slice(-4),m=o+s,r=await E.getCaptcha(m);if(console.log("验证码API响应:",r),r.code===200||r.success){const d=r.result||r.data;t.value=d,v.value=m,console.log("验证码base64数据长度:",d==null?void 0:d.length),console.log("验证码checkKey:",m)}else throw new Error(r.msg||"获取验证码失败")}catch(o){console.error("获取验证码失败:",o),c.error("获取验证码失败，请稍后重试")}},h=()=>{e.captcha="",y()},M=()=>{console.error("验证码图片加载失败")},k=localStorage.getItem("freedomRememberedUsername"),V=O("freedomRememberedPassword");return k&&V&&(e.username=k,e.password=V,e.rememberMe=!0),y(),(o,s)=>{const m=H,r=G,d=J,F=Q,L=A;return w(),C(L,{ref_key:"passwordFormRef",ref:g,model:e,rules:I,"validate-on-rule-change":!1,class:"login-form"},{default:l(()=>[n(r,{prop:"username"},{default:l(()=>[n(m,{modelValue:e.username,"onUpdate:modelValue":s[0]||(s[0]=i=>e.username=i),placeholder:"请输入用户名",size:"large",class:"login-input"},null,8,["modelValue"])]),_:1}),n(r,{prop:"password",class:"password"},{default:l(()=>[n(m,{modelValue:e.password,"onUpdate:modelValue":s[1]||(s[1]=i=>e.password=i),type:"password",placeholder:"请输入登录密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),n(r,{prop:"captcha",class:"captcha"},{default:l(()=>[a("div",Z,[n(m,{modelValue:e.captcha,"onUpdate:modelValue":s[2]||(s[2]=i=>e.captcha=i),placeholder:"请输入验证码",size:"large",class:"captcha-input",maxlength:"4"},null,8,["modelValue"]),a("div",{class:"captcha-image-wrapper",onClick:h},[a("img",{src:t.value,alt:"验证码",class:"captcha-image",onError:M},null,40,ee),s[4]||(s[4]=a("div",{class:"captcha-refresh-tip"},"点击刷新",-1))])])]),_:1}),n(r,{class:"form-options"},{default:l(()=>[n(d,{modelValue:e.rememberMe,"onUpdate:modelValue":s[3]||(s[3]=i=>e.rememberMe=i),class:"remember-checkbox"},{default:l(()=>s[5]||(s[5]=[S(" 记住密码 ")])),_:1,__:[5]},8,["modelValue"])]),_:1}),n(r,null,{default:l(()=>[n(F,{type:"primary",size:"large",class:"login-button",loading:u.value,onClick:P},{default:l(()=>s[6]||(s[6]=[S(" 登录 ")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1},8,["model"])}}}),oe=U(se,[["__scopeId","data-v-937fcd62"]]),re={class:"login-container"},ae={class:"login-right"},te={class:"login-form-container"},ne={class:"form-logo"},le={class:"back-link"},ce=x({__name:"index",setup(R){const p=j(),b=D(),_=B(()=>oe),u=()=>{const e=b.query.returnUrl;e?p.push(e):p.push("/freedom/freedomHome")},g=()=>{p.push("/login")};return(e,t)=>(w(),q("div",re,[t[3]||(t[3]=z('<div class="login-left" data-v-674bbe25><div class="logo-3d-wrapper" data-v-674bbe25><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1_1754963030315.png" alt="" class="logo-3d-image-1" data-v-674bbe25><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/2_1754963119472.png" alt="" class="logo-3d-image-2" data-v-674bbe25><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/3_1754963135630.png" alt="" class="logo-3d-image-3" data-v-674bbe25></div></div>',1)),a("div",ae,[a("div",te,[a("div",ne,[n(T,{iconName:"login-logo",className:"logo-icon"})]),t[1]||(t[1]=a("h2",{class:"welcome-title"},"回收商登录",-1)),t[2]||(t[2]=a("p",{class:"login-subtitle"},"请使用回收商账号登录",-1)),(w(),C(K(_.value),{onLoginSuccess:u},null,32)),a("div",le,[t[0]||(t[0]=a("span",null,"返回",-1)),a("a",{href:"#",onClick:$(g,["prevent"])},"主站登录")])])])]))}}),Ee=U(ce,[["__scopeId","data-v-674bbe25"]]);export{Ee as default};
