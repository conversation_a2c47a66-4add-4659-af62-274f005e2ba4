import{S as t}from"./ServiceSupport-VNFTK4S1.js";import{d as e,a as o,I as s,o as r}from"./vue-vendor-D6tHD5lA.js";import{_ as i}from"./index-CCmno-0X.js";import"./element-plus-BiAL0NdQ.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vue-router-C0lzQS1p.js";import"./utils-common-PdkFOSu3.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";import"./app-stores-CLUCXxRF.js";const p={class:"page"},a=i(e({__name:"FreedomSupport",setup(e){const i=[{icon:"support-question",text:"常见问题"},{icon:"support-freedom-account",text:"结算规则"},{icon:"support-freedom-flow",text:"自由交易流程"},{icon:"support-freedom-rule",text:"自由交易规则"}],a=[[{title:"如何注册账户？",answer:'您可以点击页面右上角的"注册"按钮，填写相关信息完成注册。'},{title:"忘记密码怎么办？",answer:'可以通过"忘记密码"功能重置密码，系统会发送验证码到您的手机。'}],[{title:"如何完成实名认证？",answer:"进入个人中心，上传身份证照片并填写真实信息即可。"}],[{title:"支持哪些支付方式？",answer:"支持支付宝、微信支付、银行卡等多种支付方式。"}],[{title:"如何参与竞拍？",answer:"完成实名认证后，缴纳保证金即可参与竞拍。"}]],n=t=>{alert(`搜索: ${t}`)},m=async t=>(await new Promise(t=>setTimeout(t,300)),a[t]||[]),u=(t,e)=>{alert(e?"感谢您的反馈！":"我们会继续改进！")};return(e,a)=>(r(),o("div",p,[s(t,{title:"嗨！有什么需要帮忙的吗？",placeholder:"搜索答案","navigation-items":i,"on-search":n,"on-get-questions":m,"on-helpful-feedback":u})]))}}),[["__scopeId","data-v-2c881115"]]);export{a as default};
