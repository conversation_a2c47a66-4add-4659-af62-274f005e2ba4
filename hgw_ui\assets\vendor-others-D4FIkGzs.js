import{z as _r,i as Tr,aw as Mr,u as Xt,r as At,af as Er,t as Sr,M as jr,_ as Rr,R as qr,g as Fr}from"./vue-vendor-2E6AJATX.js";function Qe(t,e={},r){for(const n in t){const i=t[n],o=r?`${r}:${n}`:n;typeof i=="object"&&i!==null?Qe(i,e,o):typeof i=="function"&&(e[o]=i)}return e}const Dr={run:t=>t()},Lr=()=>Dr,Qt=typeof console.createTask<"u"?console.createTask:Lr;function Cr(t,e){const r=e.shift(),n=Qt(r);return t.reduce((i,o)=>i.then(()=>n.run(()=>o(...e))),Promise.resolve())}function Wr(t,e){const r=e.shift(),n=Qt(r);return Promise.all(t.map(i=>n.run(()=>i(...e))))}function Ke(t,e){for(const r of[...t])r(e)}class $r{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,r,n={}){if(!e||typeof r!="function")return()=>{};const i=e;let o;for(;this._deprecatedHooks[e];)o=this._deprecatedHooks[e],e=o.to;if(o&&!n.allowDeprecated){let a=o.message;a||(a=`${i} hook has been deprecated`+(o.to?`, please use ${o.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(a)||(console.warn(a),this._deprecatedMessages.add(a))}if(!r.name)try{Object.defineProperty(r,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(r),()=>{r&&(this.removeHook(e,r),r=void 0)}}hookOnce(e,r){let n,i=(...o)=>(typeof n=="function"&&n(),n=void 0,i=void 0,r(...o));return n=this.hook(e,i),n}removeHook(e,r){if(this._hooks[e]){const n=this._hooks[e].indexOf(r);n!==-1&&this._hooks[e].splice(n,1),this._hooks[e].length===0&&delete this._hooks[e]}}deprecateHook(e,r){this._deprecatedHooks[e]=typeof r=="string"?{to:r}:r;const n=this._hooks[e]||[];delete this._hooks[e];for(const i of n)this.hook(e,i)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(const r in e)this.deprecateHook(r,e[r])}addHooks(e){const r=Qe(e),n=Object.keys(r).map(i=>this.hook(i,r[i]));return()=>{for(const i of n.splice(0,n.length))i()}}removeHooks(e){const r=Qe(e);for(const n in r)this.removeHook(n,r[n])}removeAllHooks(){for(const e in this._hooks)delete this._hooks[e]}callHook(e,...r){return r.unshift(e),this.callHookWith(Cr,e,...r)}callHookParallel(e,...r){return r.unshift(e),this.callHookWith(Wr,e,...r)}callHookWith(e,r,...n){const i=this._before||this._after?{name:r,args:n,context:{}}:void 0;this._before&&Ke(this._before,i);const o=e(r in this._hooks?[...this._hooks[r]]:[],n);return o instanceof Promise?o.finally(()=>{this._after&&i&&Ke(this._after,i)}):(this._after&&i&&Ke(this._after,i),o)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(this._before!==void 0){const r=this._before.indexOf(e);r!==-1&&this._before.splice(r,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(this._after!==void 0){const r=this._after.indexOf(e);r!==-1&&this._after.splice(r,1)}}}}function Br(){return new $r}const Nr=new Set(["title","titleTemplate","script","style","noscript"]),Fe=new Set(["base","meta","link","style","script","noscript"]),Ir=new Set(["title","titleTemplate","templateParams","base","htmlAttrs","bodyAttrs","meta","link","style","script","noscript"]),Vr=new Set(["base","title","titleTemplate","bodyAttrs","htmlAttrs","templateParams"]),Yt=new Set(["tagPosition","tagPriority","tagDuplicateStrategy","children","innerHTML","textContent","processTemplateParams"]),Ur=typeof window<"u";function Ce(t){let e=9;for(let r=0;r<t.length;)e=Math.imul(e^t.charCodeAt(r++),9**9);return((e^e>>>9)+65536).toString(16).substring(1,8).toLowerCase()}function Ye(t){if(t._h)return t._h;if(t._d)return Ce(t._d);let e=`${t.tag}:${t.textContent||t.innerHTML||""}:`;for(const r in t.props)e+=`${r}:${String(t.props[r])},`;return Ce(e)}function Gr(t,e){return t instanceof Promise?t.then(e):e(t)}function et(t,e,r,n){const i=n||tr(typeof e=="object"&&typeof e!="function"&&!(e instanceof Promise)?{...e}:{[t==="script"||t==="noscript"||t==="style"?"innerHTML":"textContent"]:e},t==="templateParams"||t==="titleTemplate");if(i instanceof Promise)return i.then(a=>et(t,e,r,a));const o={tag:t,props:i};for(const a of Yt){const s=o.props[a]!==void 0?o.props[a]:r[a];s!==void 0&&((!(a==="innerHTML"||a==="textContent"||a==="children")||Nr.has(o.tag))&&(o[a==="children"?"innerHTML":a]=s),delete o.props[a])}return o.props.body&&(o.tagPosition="bodyClose",delete o.props.body),o.tag==="script"&&typeof o.innerHTML=="object"&&(o.innerHTML=JSON.stringify(o.innerHTML),o.props.type=o.props.type||"application/json"),Array.isArray(o.props.content)?o.props.content.map(a=>({...o,props:{...o.props,content:a}})):o}function Kr(t,e){var n;const r=t==="class"?" ":";";return e&&typeof e=="object"&&!Array.isArray(e)&&(e=Object.entries(e).filter(([,i])=>i).map(([i,o])=>t==="style"?`${i}:${o}`:i)),(n=String(Array.isArray(e)?e.join(r):e))==null?void 0:n.split(r).filter(i=>!!i.trim()).join(r)}function er(t,e,r,n){for(let i=n;i<r.length;i+=1){const o=r[i];if(o==="class"||o==="style"){t[o]=Kr(o,t[o]);continue}if(t[o]instanceof Promise)return t[o].then(a=>(t[o]=a,er(t,e,r,i)));if(!e&&!Yt.has(o)){const a=String(t[o]),s=o.startsWith("data-");a==="true"||a===""?t[o]=s?"true":!0:t[o]||(s&&a==="false"?t[o]="false":delete t[o])}}}function tr(t,e=!1){const r=er(t,e,Object.keys(t),0);return r instanceof Promise?r.then(()=>t):t}const Jr=10;function rr(t,e,r){for(let n=r;n<e.length;n+=1){const i=e[n];if(i instanceof Promise)return i.then(o=>(e[n]=o,rr(t,e,n)));Array.isArray(i)?t.push(...i):t.push(i)}}function Zr(t){const e=[],r=t.resolvedInput;for(const i in r){if(!Object.prototype.hasOwnProperty.call(r,i))continue;const o=r[i];if(!(o===void 0||!Ir.has(i))){if(Array.isArray(o)){for(const a of o)e.push(et(i,a,t));continue}e.push(et(i,o,t))}}if(e.length===0)return[];const n=[];return Gr(rr(n,e,0),()=>n.map((i,o)=>(i._e=t._i,t.mode&&(i._m=t.mode),i._p=(t._i<<Jr)+o,i)))}const Pt=new Set(["onload","onerror","onabort","onprogress","onloadstart"]),_t={base:-10,title:10},Tt={critical:-80,high:-10,low:20};function We(t){const e=t.tagPriority;if(typeof e=="number")return e;let r=100;return t.tag==="meta"?t.props["http-equiv"]==="content-security-policy"?r=-30:t.props.charset?r=-20:t.props.name==="viewport"&&(r=-15):t.tag==="link"&&t.props.rel==="preconnect"?r=20:t.tag in _t&&(r=_t[t.tag]),e&&e in Tt?r+Tt[e]:r}const zr=[{prefix:"before:",offset:-1},{prefix:"after:",offset:1}],Xr=["name","property","http-equiv"];function nr(t){const{props:e,tag:r}=t;if(Vr.has(r))return r;if(r==="link"&&e.rel==="canonical")return"canonical";if(e.charset)return"charset";if(e.id)return`${r}:id:${e.id}`;for(const n of Xr)if(e[n]!==void 0)return`${r}:${n}:${e[n]}`;return!1}const Z="%separator";function Qr(t,e,r=!1){var i;let n;if(e==="s"||e==="pageTitle")n=t.pageTitle;else if(e.includes(".")){const o=e.indexOf(".");n=(i=t[e.substring(0,o)])==null?void 0:i[e.substring(o+1)]}else n=t[e];if(n!==void 0)return r?(n||"").replace(/"/g,'\\"'):n||""}const Yr=new RegExp(`${Z}(?:\\s*${Z})*`,"g");function Ee(t,e,r,n=!1){if(typeof t!="string"||!t.includes("%"))return t;let i=t;try{i=decodeURI(t)}catch{}const o=i.match(/%\w+(?:\.\w+)?/g);if(!o)return t;const a=t.includes(Z);return t=t.replace(/%\w+(?:\.\w+)?/g,s=>{if(s===Z||!o.includes(s))return s;const f=Qr(e,s.slice(1),n);return f!==void 0?f:s}).trim(),a&&(t.endsWith(Z)&&(t=t.slice(0,-Z.length)),t.startsWith(Z)&&(t=t.slice(Z.length)),t=t.replace(Yr,r).trim()),t}function Mt(t,e){return t==null?e||null:typeof t=="function"?t(e):t}async function en(t,e={}){const r=e.document||t.resolvedOptions.document;if(!r||!t.dirty)return;const n={shouldRender:!0,tags:[]};if(await t.hooks.callHook("dom:beforeRender",n),!!n.shouldRender)return t._domUpdatePromise||(t._domUpdatePromise=new Promise(async i=>{var b;const o=(await t.resolveTags()).map(p=>({tag:p,id:Fe.has(p.tag)?Ye(p):p.tag,shouldRender:!0}));let a=t._dom;if(!a){a={elMap:{htmlAttrs:r.documentElement,bodyAttrs:r.body}};const p=new Set;for(const h of["body","head"]){const y=(b=r[h])==null?void 0:b.children;for(const u of y){const v=u.tagName.toLowerCase();if(!Fe.has(v))continue;const d={tag:v,props:await tr(u.getAttributeNames().reduce((w,x)=>({...w,[x]:u.getAttribute(x)}),{})),innerHTML:u.innerHTML},k=nr(d);let g=k,m=1;for(;g&&p.has(g);)g=`${k}:${m++}`;g&&(d._d=g,p.add(g)),a.elMap[u.getAttribute("data-hid")||Ye(d)]=u}}}a.pendingSideEffects={...a.sideEffects},a.sideEffects={};function s(p,h,y){const u=`${p}:${h}`;a.sideEffects[u]=y,delete a.pendingSideEffects[u]}function f({id:p,$el:h,tag:y}){const u=y.tag.endsWith("Attrs");if(a.elMap[p]=h,u||(y.textContent&&y.textContent!==h.textContent&&(h.textContent=y.textContent),y.innerHTML&&y.innerHTML!==h.innerHTML&&(h.innerHTML=y.innerHTML),s(p,"el",()=>{var v;(v=a.elMap[p])==null||v.remove(),delete a.elMap[p]})),y._eventHandlers)for(const v in y._eventHandlers)Object.prototype.hasOwnProperty.call(y._eventHandlers,v)&&h.getAttribute(`data-${v}`)!==""&&((y.tag==="bodyAttrs"?r.defaultView:h).addEventListener(v.substring(2),y._eventHandlers[v].bind(h)),h.setAttribute(`data-${v}`,""));for(const v in y.props){if(!Object.prototype.hasOwnProperty.call(y.props,v))continue;const d=y.props[v],k=`attr:${v}`;if(v==="class"){if(!d)continue;for(const g of d.split(" "))u&&s(p,`${k}:${g}`,()=>h.classList.remove(g)),!h.classList.contains(g)&&h.classList.add(g)}else if(v==="style"){if(!d)continue;for(const g of d.split(";")){const m=g.indexOf(":"),w=g.substring(0,m).trim(),x=g.substring(m+1).trim();s(p,`${k}:${w}`,()=>{h.style.removeProperty(w)}),h.style.setProperty(w,x)}}else h.getAttribute(v)!==d&&h.setAttribute(v,d===!0?"":String(d)),u&&s(p,k,()=>h.removeAttribute(v))}}const c=[],l={bodyClose:void 0,bodyOpen:void 0,head:void 0};for(const p of o){const{tag:h,shouldRender:y,id:u}=p;if(y){if(h.tag==="title"){r.title=h.textContent;continue}p.$el=p.$el||a.elMap[u],p.$el?f(p):Fe.has(h.tag)&&c.push(p)}}for(const p of c){const h=p.tag.tagPosition||"head";p.$el=r.createElement(p.tag.tag),f(p),l[h]=l[h]||r.createDocumentFragment(),l[h].appendChild(p.$el)}for(const p of o)await t.hooks.callHook("dom:renderTag",p,r,s);l.head&&r.head.appendChild(l.head),l.bodyOpen&&r.body.insertBefore(l.bodyOpen,r.body.firstChild),l.bodyClose&&r.body.appendChild(l.bodyClose);for(const p in a.pendingSideEffects)a.pendingSideEffects[p]();t._dom=a,await t.hooks.callHook("dom:rendered",{renders:o}),i()}).finally(()=>{t._domUpdatePromise=void 0,t.dirty=!1})),t._domUpdatePromise}function tn(t,e={}){const r=e.delayFn||(n=>setTimeout(n,10));return t._domDebouncedUpdatePromise=t._domDebouncedUpdatePromise||new Promise(n=>r(()=>en(t,e).then(()=>{delete t._domDebouncedUpdatePromise,n()})))}function rn(t){return e=>{var n,i;const r=((i=(n=e.resolvedOptions.document)==null?void 0:n.head.querySelector('script[id="unhead:payload"]'))==null?void 0:i.innerHTML)||!1;return r&&e.push(JSON.parse(r)),{mode:"client",hooks:{"entries:updated":o=>{tn(o,t)}}}}}const nn=new Set(["templateParams","htmlAttrs","bodyAttrs"]),on={hooks:{"tag:normalise":({tag:t})=>{t.props.hid&&(t.key=t.props.hid,delete t.props.hid),t.props.vmid&&(t.key=t.props.vmid,delete t.props.vmid),t.props.key&&(t.key=t.props.key,delete t.props.key);const e=nr(t);e&&!e.startsWith("meta:og:")&&!e.startsWith("meta:twitter:")&&delete t.key;const r=e||(t.key?`${t.tag}:${t.key}`:!1);r&&(t._d=r)},"tags:resolve":t=>{const e=Object.create(null);for(const n of t.tags){const i=(n.key?`${n.tag}:${n.key}`:n._d)||Ye(n),o=e[i];if(o){let s=n==null?void 0:n.tagDuplicateStrategy;if(!s&&nn.has(n.tag)&&(s="merge"),s==="merge"){const f=o.props;f.style&&n.props.style&&(f.style[f.style.length-1]!==";"&&(f.style+=";"),n.props.style=`${f.style} ${n.props.style}`),f.class&&n.props.class?n.props.class=`${f.class} ${n.props.class}`:f.class&&(n.props.class=f.class),e[i].props={...f,...n.props};continue}else if(n._e===o._e){o._duped=o._duped||[],n._d=`${o._d}:${o._duped.length+1}`,o._duped.push(n);continue}else if(We(n)>We(o))continue}if(!(n.innerHTML||n.textContent||Object.keys(n.props).length!==0)&&Fe.has(n.tag)){delete e[i];continue}e[i]=n}const r=[];for(const n in e){const i=e[n],o=i._duped;r.push(i),o&&(delete i._duped,r.push(...o))}t.tags=r,t.tags=t.tags.filter(n=>!(n.tag==="meta"&&(n.props.name||n.props.property)&&!n.props.content))}}},an=new Set(["script","link","bodyAttrs"]),sn=t=>({hooks:{"tags:resolve":e=>{for(const r of e.tags){if(!an.has(r.tag))continue;const n=r.props;for(const i in n){if(i[0]!=="o"||i[1]!=="n"||!Object.prototype.hasOwnProperty.call(n,i))continue;const o=n[i];typeof o=="function"&&(t.ssr&&Pt.has(i)?n[i]=`this.dataset.${i}fired = true`:delete n[i],r._eventHandlers=r._eventHandlers||{},r._eventHandlers[i]=o)}t.ssr&&r._eventHandlers&&(r.props.src||r.props.href)&&(r.key=r.key||Ce(r.props.src||r.props.href))}},"dom:renderTag":({$el:e,tag:r})=>{var i,o;const n=e==null?void 0:e.dataset;if(n)for(const a in n){if(!a.endsWith("fired"))continue;const s=a.slice(0,-5);Pt.has(s)&&((o=(i=r._eventHandlers)==null?void 0:i[s])==null||o.call(e,new Event(s.substring(2))))}}}}),fn=new Set(["link","style","script","noscript"]),cn={hooks:{"tag:normalise":({tag:t})=>{t.key&&fn.has(t.tag)&&(t.props["data-hid"]=t._h=Ce(t.key))}}},un={mode:"server",hooks:{"tags:beforeResolve":t=>{const e={};let r=!1;for(const n of t.tags)n._m!=="server"||n.tag!=="titleTemplate"&&n.tag!=="templateParams"&&n.tag!=="title"||(e[n.tag]=n.tag==="title"||n.tag==="titleTemplate"?n.textContent:n.props,r=!0);r&&t.tags.push({tag:"script",innerHTML:JSON.stringify(e),props:{id:"unhead:payload",type:"application/json"}})}}},dn={hooks:{"tags:resolve":t=>{var e;for(const r of t.tags)if(typeof r.tagPriority=="string")for(const{prefix:n,offset:i}of zr){if(!r.tagPriority.startsWith(n))continue;const o=r.tagPriority.substring(n.length),a=(e=t.tags.find(s=>s._d===o))==null?void 0:e._p;if(a!==void 0){r._p=a+i;break}}t.tags.sort((r,n)=>{const i=We(r),o=We(n);return i<o?-1:i>o?1:r._p-n._p})}}},ln={meta:"content",link:"href",htmlAttrs:"lang"},pn=["innerHTML","textContent"],hn=t=>({hooks:{"tags:resolve":e=>{var a;const{tags:r}=e;let n;for(let s=0;s<r.length;s+=1)r[s].tag==="templateParams"&&(n=e.tags.splice(s,1)[0].props,s-=1);const i=n||{},o=i.separator||"|";delete i.separator,i.pageTitle=Ee(i.pageTitle||((a=r.find(s=>s.tag==="title"))==null?void 0:a.textContent)||"",i,o);for(const s of r){if(s.processTemplateParams===!1)continue;const f=ln[s.tag];if(f&&typeof s.props[f]=="string")s.props[f]=Ee(s.props[f],i,o);else if(s.processTemplateParams||s.tag==="titleTemplate"||s.tag==="title")for(const c of pn)typeof s[c]=="string"&&(s[c]=Ee(s[c],i,o,s.tag==="script"&&s.props.type.endsWith("json")))}t._templateParams=i,t._separator=o},"tags:afterResolve":({tags:e})=>{let r;for(let n=0;n<e.length;n+=1){const i=e[n];i.tag==="title"&&i.processTemplateParams!==!1&&(r=i)}r!=null&&r.textContent&&(r.textContent=Ee(r.textContent,t._templateParams,t._separator))}}}),gn={hooks:{"tags:resolve":t=>{const{tags:e}=t;let r,n;for(let i=0;i<e.length;i+=1){const o=e[i];o.tag==="title"?r=o:o.tag==="titleTemplate"&&(n=o)}if(n&&r){const i=Mt(n.textContent,r.textContent);i!==null?r.textContent=i||r.textContent:t.tags.splice(t.tags.indexOf(r),1)}else if(n){const i=Mt(n.textContent);i!==null&&(n.textContent=i,n.tag="title",n=void 0)}n&&t.tags.splice(t.tags.indexOf(n),1)}}},vn={hooks:{"tags:afterResolve":t=>{for(const e of t.tags)typeof e.innerHTML=="string"&&(e.innerHTML&&(e.props.type==="application/ld+json"||e.props.type==="application/json")?e.innerHTML=e.innerHTML.replace(/</g,"\\u003C"):e.innerHTML=e.innerHTML.replace(new RegExp(`</${e.tag}`,"g"),`<\\/${e.tag}`))}}};let ir;function yn(t={}){const e=mn(t);return e.use(rn()),ir=e}function Et(t,e){return!t||t==="server"&&e||t==="client"&&!e}function mn(t={}){const e=Br();e.addHooks(t.hooks||{}),t.document=t.document||(Ur?document:void 0);const r=!t.document,n=()=>{s.dirty=!0,e.callHook("entries:updated",s)};let i=0,o=[];const a=[],s={plugins:a,dirty:!1,resolvedOptions:t,hooks:e,headEntries(){return o},use(f){const c=typeof f=="function"?f(s):f;(!c.key||!a.some(l=>l.key===c.key))&&(a.push(c),Et(c.mode,r)&&e.addHooks(c.hooks||{}))},push(f,c){c==null||delete c.head;const l={_i:i++,input:f,...c};return Et(l.mode,r)&&(o.push(l),n()),{dispose(){o=o.filter(b=>b._i!==l._i),n()},patch(b){for(const p of o)p._i===l._i&&(p.input=l.input=b);n()}}},async resolveTags(){const f={tags:[],entries:[...o]};await e.callHook("entries:resolve",f);for(const c of f.entries){const l=c.resolvedInput||c.input;if(c.resolvedInput=await(c.transform?c.transform(l):l),c.resolvedInput)for(const b of await Zr(c)){const p={tag:b,entry:c,resolvedOptions:s.resolvedOptions};await e.callHook("tag:normalise",p),f.tags.push(p.tag)}}return await e.callHook("tags:beforeResolve",f),await e.callHook("tags:resolve",f),await e.callHook("tags:afterResolve",f),f.tags},ssr:r};return[on,un,sn,cn,dn,hn,gn,vn,...(t==null?void 0:t.plugins)||[]].forEach(f=>s.use(f)),s.hooks.callHook("init",s),s}function bn(){return ir}const wn=Mr[0]==="3";function xn(t){return typeof t=="function"?t():Xt(t)}function $e(t){if(t instanceof Promise||t instanceof Date||t instanceof RegExp)return t;const e=xn(t);if(!t||!e)return e;if(Array.isArray(e))return e.map(r=>$e(r));if(typeof e=="object"){const r={};for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(n==="titleTemplate"||n[0]==="o"&&n[1]==="n"){r[n]=Xt(e[n]);continue}r[n]=$e(e[n])}return r}return e}const kn={hooks:{"entries:resolve":t=>{for(const e of t.entries)e.resolvedInput=$e(e.input)}}},or="usehead";function On(t){return{install(r){wn&&(r.config.globalProperties.$unhead=t,r.config.globalProperties.$head=t,r.provide(or,t))}}.install}function wo(t={}){t.domDelayFn=t.domDelayFn||(r=>_r(()=>setTimeout(()=>r(),0)));const e=yn(t);return e.use(kn),e.install=On(e),e}const St=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},jt="__unhead_injection_handler__";function Hn(){return jt in St?St[jt]():Tr(or)||bn()}function An(t,e={}){const r=e.head||Hn();if(r)return r.ssr?r.push(t,e):Pn(r,t,e)}function Pn(t,e,r={}){const n=At(!1),i=At({});Er(()=>{i.value=n.value?{}:$e(e)});const o=t.push(i.value,r);return Sr(i,s=>{o.patch(s)}),Fr()&&(jr(()=>{o.dispose()}),Rr(()=>{n.value=!0}),qr(()=>{n.value=!1})),o}function xo(t){const e=t;return e.headTags=t.resolveTags,e.addEntry=t.push,e.addHeadObjs=t.push,e.addReactiveEntry=(r,n)=>{const i=An(r,n);return i!==void 0?i.dispose:()=>{}},e.removeHeadObjs=()=>{},e.updateDOM=()=>{t.hooks.callHook("entries:updated",t)},e.unhead=t,e}var R="top",W="bottom",$="right",q="left",ft="auto",Oe=[R,W,$,q],se="start",we="end",_n="clippingParents",ar="viewport",ge="popper",Tn="reference",Rt=Oe.reduce(function(t,e){return t.concat([e+"-"+se,e+"-"+we])},[]),sr=[].concat(Oe,[ft]).reduce(function(t,e){return t.concat([e,e+"-"+se,e+"-"+we])},[]),Mn="beforeRead",En="read",Sn="afterRead",jn="beforeMain",Rn="main",qn="afterMain",Fn="beforeWrite",Dn="write",Ln="afterWrite",Cn=[Mn,En,Sn,jn,Rn,qn,Fn,Dn,Ln];function U(t){return t?(t.nodeName||"").toLowerCase():null}function I(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function fe(t){var e=I(t).Element;return t instanceof e||t instanceof Element}function C(t){var e=I(t).HTMLElement;return t instanceof e||t instanceof HTMLElement}function ct(t){if(typeof ShadowRoot>"u")return!1;var e=I(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}function Wn(t){var e=t.state;Object.keys(e.elements).forEach(function(r){var n=e.styles[r]||{},i=e.attributes[r]||{},o=e.elements[r];!C(o)||!U(o)||(Object.assign(o.style,n),Object.keys(i).forEach(function(a){var s=i[a];s===!1?o.removeAttribute(a):o.setAttribute(a,s===!0?"":s)}))})}function $n(t){var e=t.state,r={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,r.popper),e.styles=r,e.elements.arrow&&Object.assign(e.elements.arrow.style,r.arrow),function(){Object.keys(e.elements).forEach(function(n){var i=e.elements[n],o=e.attributes[n]||{},a=Object.keys(e.styles.hasOwnProperty(n)?e.styles[n]:r[n]),s=a.reduce(function(f,c){return f[c]="",f},{});!C(i)||!U(i)||(Object.assign(i.style,s),Object.keys(o).forEach(function(f){i.removeAttribute(f)}))})}}var fr={name:"applyStyles",enabled:!0,phase:"write",fn:Wn,effect:$n,requires:["computeStyles"]};function V(t){return t.split("-")[0]}var ie=Math.max,Be=Math.min,ce=Math.round;function ue(t,e){e===void 0&&(e=!1);var r=t.getBoundingClientRect(),n=1,i=1;if(C(t)&&e){var o=t.offsetHeight,a=t.offsetWidth;a>0&&(n=ce(r.width)/a||1),o>0&&(i=ce(r.height)/o||1)}return{width:r.width/n,height:r.height/i,top:r.top/i,right:r.right/n,bottom:r.bottom/i,left:r.left/n,x:r.left/n,y:r.top/i}}function ut(t){var e=ue(t),r=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-r)<=1&&(r=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:r,height:n}}function cr(t,e){var r=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(r&&ct(r)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function J(t){return I(t).getComputedStyle(t)}function Bn(t){return["table","td","th"].indexOf(U(t))>=0}function X(t){return((fe(t)?t.ownerDocument:t.document)||window.document).documentElement}function Ne(t){return U(t)==="html"?t:t.assignedSlot||t.parentNode||(ct(t)?t.host:null)||X(t)}function qt(t){return!C(t)||J(t).position==="fixed"?null:t.offsetParent}function Nn(t){var e=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,r=navigator.userAgent.indexOf("Trident")!==-1;if(r&&C(t)){var n=J(t);if(n.position==="fixed")return null}var i=Ne(t);for(ct(i)&&(i=i.host);C(i)&&["html","body"].indexOf(U(i))<0;){var o=J(i);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||e&&o.willChange==="filter"||e&&o.filter&&o.filter!=="none")return i;i=i.parentNode}return null}function He(t){for(var e=I(t),r=qt(t);r&&Bn(r)&&J(r).position==="static";)r=qt(r);return r&&(U(r)==="html"||U(r)==="body"&&J(r).position==="static")?e:r||Nn(t)||e}function dt(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function ye(t,e,r){return ie(t,Be(e,r))}function In(t,e,r){var n=ye(t,e,r);return n>r?r:n}function ur(){return{top:0,right:0,bottom:0,left:0}}function dr(t){return Object.assign({},ur(),t)}function lr(t,e){return e.reduce(function(r,n){return r[n]=t,r},{})}var Vn=function(t,e){return t=typeof t=="function"?t(Object.assign({},e.rects,{placement:e.placement})):t,dr(typeof t!="number"?t:lr(t,Oe))};function Un(t){var e,r=t.state,n=t.name,i=t.options,o=r.elements.arrow,a=r.modifiersData.popperOffsets,s=V(r.placement),f=dt(s),c=[q,$].indexOf(s)>=0,l=c?"height":"width";if(!(!o||!a)){var b=Vn(i.padding,r),p=ut(o),h=f==="y"?R:q,y=f==="y"?W:$,u=r.rects.reference[l]+r.rects.reference[f]-a[f]-r.rects.popper[l],v=a[f]-r.rects.reference[f],d=He(o),k=d?f==="y"?d.clientHeight||0:d.clientWidth||0:0,g=u/2-v/2,m=b[h],w=k-p[l]-b[y],x=k/2-p[l]/2+g,H=ye(m,x,w),A=f;r.modifiersData[n]=(e={},e[A]=H,e.centerOffset=H-x,e)}}function Gn(t){var e=t.state,r=t.options,n=r.element,i=n===void 0?"[data-popper-arrow]":n;i!=null&&(typeof i=="string"&&(i=e.elements.popper.querySelector(i),!i)||!cr(e.elements.popper,i)||(e.elements.arrow=i))}var Kn={name:"arrow",enabled:!0,phase:"main",fn:Un,effect:Gn,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function de(t){return t.split("-")[1]}var Jn={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Zn(t){var e=t.x,r=t.y,n=window,i=n.devicePixelRatio||1;return{x:ce(e*i)/i||0,y:ce(r*i)/i||0}}function Ft(t){var e,r=t.popper,n=t.popperRect,i=t.placement,o=t.variation,a=t.offsets,s=t.position,f=t.gpuAcceleration,c=t.adaptive,l=t.roundOffsets,b=t.isFixed,p=a.x,h=p===void 0?0:p,y=a.y,u=y===void 0?0:y,v=typeof l=="function"?l({x:h,y:u}):{x:h,y:u};h=v.x,u=v.y;var d=a.hasOwnProperty("x"),k=a.hasOwnProperty("y"),g=q,m=R,w=window;if(c){var x=He(r),H="clientHeight",A="clientWidth";if(x===I(r)&&(x=X(r),J(x).position!=="static"&&s==="absolute"&&(H="scrollHeight",A="scrollWidth")),x=x,i===R||(i===q||i===$)&&o===we){m=W;var T=b&&x===w&&w.visualViewport?w.visualViewport.height:x[H];u-=T-n.height,u*=f?1:-1}if(i===q||(i===R||i===W)&&o===we){g=$;var S=b&&x===w&&w.visualViewport?w.visualViewport.width:x[A];h-=S-n.width,h*=f?1:-1}}var M=Object.assign({position:s},c&&Jn),_=l===!0?Zn({x:h,y:u}):{x:h,y:u};if(h=_.x,u=_.y,f){var P;return Object.assign({},M,(P={},P[m]=k?"0":"",P[g]=d?"0":"",P.transform=(w.devicePixelRatio||1)<=1?"translate("+h+"px, "+u+"px)":"translate3d("+h+"px, "+u+"px, 0)",P))}return Object.assign({},M,(e={},e[m]=k?u+"px":"",e[g]=d?h+"px":"",e.transform="",e))}function zn(t){var e=t.state,r=t.options,n=r.gpuAcceleration,i=n===void 0?!0:n,o=r.adaptive,a=o===void 0?!0:o,s=r.roundOffsets,f=s===void 0?!0:s,c={placement:V(e.placement),variation:de(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,Ft(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:a,roundOffsets:f})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,Ft(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var pr={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:zn,data:{}},Se={passive:!0};function Xn(t){var e=t.state,r=t.instance,n=t.options,i=n.scroll,o=i===void 0?!0:i,a=n.resize,s=a===void 0?!0:a,f=I(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach(function(l){l.addEventListener("scroll",r.update,Se)}),s&&f.addEventListener("resize",r.update,Se),function(){o&&c.forEach(function(l){l.removeEventListener("scroll",r.update,Se)}),s&&f.removeEventListener("resize",r.update,Se)}}var hr={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Xn,data:{}},Qn={left:"right",right:"left",bottom:"top",top:"bottom"};function De(t){return t.replace(/left|right|bottom|top/g,function(e){return Qn[e]})}var Yn={start:"end",end:"start"};function Dt(t){return t.replace(/start|end/g,function(e){return Yn[e]})}function lt(t){var e=I(t),r=e.pageXOffset,n=e.pageYOffset;return{scrollLeft:r,scrollTop:n}}function pt(t){return ue(X(t)).left+lt(t).scrollLeft}function ei(t){var e=I(t),r=X(t),n=e.visualViewport,i=r.clientWidth,o=r.clientHeight,a=0,s=0;return n&&(i=n.width,o=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(a=n.offsetLeft,s=n.offsetTop)),{width:i,height:o,x:a+pt(t),y:s}}function ti(t){var e,r=X(t),n=lt(t),i=(e=t.ownerDocument)==null?void 0:e.body,o=ie(r.scrollWidth,r.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),a=ie(r.scrollHeight,r.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),s=-n.scrollLeft+pt(t),f=-n.scrollTop;return J(i||r).direction==="rtl"&&(s+=ie(r.clientWidth,i?i.clientWidth:0)-o),{width:o,height:a,x:s,y:f}}function ht(t){var e=J(t),r=e.overflow,n=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(r+i+n)}function gr(t){return["html","body","#document"].indexOf(U(t))>=0?t.ownerDocument.body:C(t)&&ht(t)?t:gr(Ne(t))}function me(t,e){var r;e===void 0&&(e=[]);var n=gr(t),i=n===((r=t.ownerDocument)==null?void 0:r.body),o=I(n),a=i?[o].concat(o.visualViewport||[],ht(n)?n:[]):n,s=e.concat(a);return i?s:s.concat(me(Ne(a)))}function tt(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function ri(t){var e=ue(t);return e.top=e.top+t.clientTop,e.left=e.left+t.clientLeft,e.bottom=e.top+t.clientHeight,e.right=e.left+t.clientWidth,e.width=t.clientWidth,e.height=t.clientHeight,e.x=e.left,e.y=e.top,e}function Lt(t,e){return e===ar?tt(ei(t)):fe(e)?ri(e):tt(ti(X(t)))}function ni(t){var e=me(Ne(t)),r=["absolute","fixed"].indexOf(J(t).position)>=0,n=r&&C(t)?He(t):t;return fe(n)?e.filter(function(i){return fe(i)&&cr(i,n)&&U(i)!=="body"}):[]}function ii(t,e,r){var n=e==="clippingParents"?ni(t):[].concat(e),i=[].concat(n,[r]),o=i[0],a=i.reduce(function(s,f){var c=Lt(t,f);return s.top=ie(c.top,s.top),s.right=Be(c.right,s.right),s.bottom=Be(c.bottom,s.bottom),s.left=ie(c.left,s.left),s},Lt(t,o));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function vr(t){var e=t.reference,r=t.element,n=t.placement,i=n?V(n):null,o=n?de(n):null,a=e.x+e.width/2-r.width/2,s=e.y+e.height/2-r.height/2,f;switch(i){case R:f={x:a,y:e.y-r.height};break;case W:f={x:a,y:e.y+e.height};break;case $:f={x:e.x+e.width,y:s};break;case q:f={x:e.x-r.width,y:s};break;default:f={x:e.x,y:e.y}}var c=i?dt(i):null;if(c!=null){var l=c==="y"?"height":"width";switch(o){case se:f[c]=f[c]-(e[l]/2-r[l]/2);break;case we:f[c]=f[c]+(e[l]/2-r[l]/2);break}}return f}function xe(t,e){e===void 0&&(e={});var r=e,n=r.placement,i=n===void 0?t.placement:n,o=r.boundary,a=o===void 0?_n:o,s=r.rootBoundary,f=s===void 0?ar:s,c=r.elementContext,l=c===void 0?ge:c,b=r.altBoundary,p=b===void 0?!1:b,h=r.padding,y=h===void 0?0:h,u=dr(typeof y!="number"?y:lr(y,Oe)),v=l===ge?Tn:ge,d=t.rects.popper,k=t.elements[p?v:l],g=ii(fe(k)?k:k.contextElement||X(t.elements.popper),a,f),m=ue(t.elements.reference),w=vr({reference:m,element:d,placement:i}),x=tt(Object.assign({},d,w)),H=l===ge?x:m,A={top:g.top-H.top+u.top,bottom:H.bottom-g.bottom+u.bottom,left:g.left-H.left+u.left,right:H.right-g.right+u.right},T=t.modifiersData.offset;if(l===ge&&T){var S=T[i];Object.keys(A).forEach(function(M){var _=[$,W].indexOf(M)>=0?1:-1,P=[R,W].indexOf(M)>=0?"y":"x";A[M]+=S[P]*_})}return A}function oi(t,e){e===void 0&&(e={});var r=e,n=r.placement,i=r.boundary,o=r.rootBoundary,a=r.padding,s=r.flipVariations,f=r.allowedAutoPlacements,c=f===void 0?sr:f,l=de(n),b=l?s?Rt:Rt.filter(function(y){return de(y)===l}):Oe,p=b.filter(function(y){return c.indexOf(y)>=0});p.length===0&&(p=b);var h=p.reduce(function(y,u){return y[u]=xe(t,{placement:u,boundary:i,rootBoundary:o,padding:a})[V(u)],y},{});return Object.keys(h).sort(function(y,u){return h[y]-h[u]})}function ai(t){if(V(t)===ft)return[];var e=De(t);return[Dt(t),e,Dt(e)]}function si(t){var e=t.state,r=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var i=r.mainAxis,o=i===void 0?!0:i,a=r.altAxis,s=a===void 0?!0:a,f=r.fallbackPlacements,c=r.padding,l=r.boundary,b=r.rootBoundary,p=r.altBoundary,h=r.flipVariations,y=h===void 0?!0:h,u=r.allowedAutoPlacements,v=e.options.placement,d=V(v),k=d===v,g=f||(k||!y?[De(v)]:ai(v)),m=[v].concat(g).reduce(function(ee,G){return ee.concat(V(G)===ft?oi(e,{placement:G,boundary:l,rootBoundary:b,padding:c,flipVariations:y,allowedAutoPlacements:u}):G)},[]),w=e.rects.reference,x=e.rects.popper,H=new Map,A=!0,T=m[0],S=0;S<m.length;S++){var M=m[S],_=V(M),P=de(M)===se,Q=[R,W].indexOf(_)>=0,le=Q?"width":"height",F=xe(e,{placement:M,boundary:l,rootBoundary:b,altBoundary:p,padding:c}),B=Q?P?$:q:P?W:R;w[le]>x[le]&&(B=De(B));var Ae=De(B),Y=[];if(o&&Y.push(F[_]<=0),s&&Y.push(F[B]<=0,F[Ae]<=0),Y.every(function(ee){return ee})){T=M,A=!1;break}H.set(M,Y)}if(A)for(var Pe=y?3:1,Ve=function(ee){var G=m.find(function(Te){var he=H.get(Te);if(he)return he.slice(0,ee).every(function(oe){return oe})});if(G)return T=G,"break"},pe=Pe;pe>0;pe--){var _e=Ve(pe);if(_e==="break")break}e.placement!==T&&(e.modifiersData[n]._skip=!0,e.placement=T,e.reset=!0)}}var fi={name:"flip",enabled:!0,phase:"main",fn:si,requiresIfExists:["offset"],data:{_skip:!1}};function Ct(t,e,r){return r===void 0&&(r={x:0,y:0}),{top:t.top-e.height-r.y,right:t.right-e.width+r.x,bottom:t.bottom-e.height+r.y,left:t.left-e.width-r.x}}function Wt(t){return[R,$,W,q].some(function(e){return t[e]>=0})}function ci(t){var e=t.state,r=t.name,n=e.rects.reference,i=e.rects.popper,o=e.modifiersData.preventOverflow,a=xe(e,{elementContext:"reference"}),s=xe(e,{altBoundary:!0}),f=Ct(a,n),c=Ct(s,i,o),l=Wt(f),b=Wt(c);e.modifiersData[r]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:l,hasPopperEscaped:b},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":b})}var ui={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:ci};function di(t,e,r){var n=V(t),i=[q,R].indexOf(n)>=0?-1:1,o=typeof r=="function"?r(Object.assign({},e,{placement:t})):r,a=o[0],s=o[1];return a=a||0,s=(s||0)*i,[q,$].indexOf(n)>=0?{x:s,y:a}:{x:a,y:s}}function li(t){var e=t.state,r=t.options,n=t.name,i=r.offset,o=i===void 0?[0,0]:i,a=sr.reduce(function(l,b){return l[b]=di(b,e.rects,o),l},{}),s=a[e.placement],f=s.x,c=s.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=f,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=a}var pi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:li};function hi(t){var e=t.state,r=t.name;e.modifiersData[r]=vr({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})}var yr={name:"popperOffsets",enabled:!0,phase:"read",fn:hi,data:{}};function gi(t){return t==="x"?"y":"x"}function vi(t){var e=t.state,r=t.options,n=t.name,i=r.mainAxis,o=i===void 0?!0:i,a=r.altAxis,s=a===void 0?!1:a,f=r.boundary,c=r.rootBoundary,l=r.altBoundary,b=r.padding,p=r.tether,h=p===void 0?!0:p,y=r.tetherOffset,u=y===void 0?0:y,v=xe(e,{boundary:f,rootBoundary:c,padding:b,altBoundary:l}),d=V(e.placement),k=de(e.placement),g=!k,m=dt(d),w=gi(m),x=e.modifiersData.popperOffsets,H=e.rects.reference,A=e.rects.popper,T=typeof u=="function"?u(Object.assign({},e.rects,{placement:e.placement})):u,S=typeof T=="number"?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),M=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,_={x:0,y:0};if(x){if(o){var P,Q=m==="y"?R:q,le=m==="y"?W:$,F=m==="y"?"height":"width",B=x[m],Ae=B+v[Q],Y=B-v[le],Pe=h?-A[F]/2:0,Ve=k===se?H[F]:A[F],pe=k===se?-A[F]:-H[F],_e=e.elements.arrow,ee=h&&_e?ut(_e):{width:0,height:0},G=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:ur(),Te=G[Q],he=G[le],oe=ye(0,H[F],ee[F]),wr=g?H[F]/2-Pe-oe-Te-S.mainAxis:Ve-oe-Te-S.mainAxis,xr=g?-H[F]/2+Pe+oe+he+S.mainAxis:pe+oe+he+S.mainAxis,Ue=e.elements.arrow&&He(e.elements.arrow),kr=Ue?m==="y"?Ue.clientTop||0:Ue.clientLeft||0:0,vt=(P=M==null?void 0:M[m])!=null?P:0,Or=B+wr-vt-kr,Hr=B+xr-vt,yt=ye(h?Be(Ae,Or):Ae,B,h?ie(Y,Hr):Y);x[m]=yt,_[m]=yt-B}if(s){var mt,Ar=m==="x"?R:q,Pr=m==="x"?W:$,te=x[w],Me=w==="y"?"height":"width",bt=te+v[Ar],wt=te-v[Pr],Ge=[R,q].indexOf(d)!==-1,xt=(mt=M==null?void 0:M[w])!=null?mt:0,kt=Ge?bt:te-H[Me]-A[Me]-xt+S.altAxis,Ot=Ge?te+H[Me]+A[Me]-xt-S.altAxis:wt,Ht=h&&Ge?In(kt,te,Ot):ye(h?kt:bt,te,h?Ot:wt);x[w]=Ht,_[w]=Ht-te}e.modifiersData[n]=_}}var yi={name:"preventOverflow",enabled:!0,phase:"main",fn:vi,requiresIfExists:["offset"]};function mi(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function bi(t){return t===I(t)||!C(t)?lt(t):mi(t)}function wi(t){var e=t.getBoundingClientRect(),r=ce(e.width)/t.offsetWidth||1,n=ce(e.height)/t.offsetHeight||1;return r!==1||n!==1}function xi(t,e,r){r===void 0&&(r=!1);var n=C(e),i=C(e)&&wi(e),o=X(e),a=ue(t,i),s={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(n||!n&&!r)&&((U(e)!=="body"||ht(o))&&(s=bi(e)),C(e)?(f=ue(e,!0),f.x+=e.clientLeft,f.y+=e.clientTop):o&&(f.x=pt(o))),{x:a.left+s.scrollLeft-f.x,y:a.top+s.scrollTop-f.y,width:a.width,height:a.height}}function ki(t){var e=new Map,r=new Set,n=[];t.forEach(function(o){e.set(o.name,o)});function i(o){r.add(o.name);var a=[].concat(o.requires||[],o.requiresIfExists||[]);a.forEach(function(s){if(!r.has(s)){var f=e.get(s);f&&i(f)}}),n.push(o)}return t.forEach(function(o){r.has(o.name)||i(o)}),n}function Oi(t){var e=ki(t);return Cn.reduce(function(r,n){return r.concat(e.filter(function(i){return i.phase===n}))},[])}function Hi(t){var e;return function(){return e||(e=new Promise(function(r){Promise.resolve().then(function(){e=void 0,r(t())})})),e}}function Ai(t){var e=t.reduce(function(r,n){var i=r[n.name];return r[n.name]=i?Object.assign({},i,n,{options:Object.assign({},i.options,n.options),data:Object.assign({},i.data,n.data)}):n,r},{});return Object.keys(e).map(function(r){return e[r]})}var $t={placement:"bottom",modifiers:[],strategy:"absolute"};function Bt(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return!e.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function gt(t){t===void 0&&(t={});var e=t,r=e.defaultModifiers,n=r===void 0?[]:r,i=e.defaultOptions,o=i===void 0?$t:i;return function(a,s,f){f===void 0&&(f=o);var c={placement:"bottom",orderedModifiers:[],options:Object.assign({},$t,o),modifiersData:{},elements:{reference:a,popper:s},attributes:{},styles:{}},l=[],b=!1,p={state:c,setOptions:function(u){var v=typeof u=="function"?u(c.options):u;y(),c.options=Object.assign({},o,c.options,v),c.scrollParents={reference:fe(a)?me(a):a.contextElement?me(a.contextElement):[],popper:me(s)};var d=Oi(Ai([].concat(n,c.options.modifiers)));return c.orderedModifiers=d.filter(function(k){return k.enabled}),h(),p.update()},forceUpdate:function(){if(!b){var u=c.elements,v=u.reference,d=u.popper;if(Bt(v,d)){c.rects={reference:xi(v,He(d),c.options.strategy==="fixed"),popper:ut(d)},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach(function(A){return c.modifiersData[A.name]=Object.assign({},A.data)});for(var k=0;k<c.orderedModifiers.length;k++){if(c.reset===!0){c.reset=!1,k=-1;continue}var g=c.orderedModifiers[k],m=g.fn,w=g.options,x=w===void 0?{}:w,H=g.name;typeof m=="function"&&(c=m({state:c,options:x,name:H,instance:p})||c)}}}},update:Hi(function(){return new Promise(function(u){p.forceUpdate(),u(c)})}),destroy:function(){y(),b=!0}};if(!Bt(a,s))return p;p.setOptions(f).then(function(u){!b&&f.onFirstUpdate&&f.onFirstUpdate(u)});function h(){c.orderedModifiers.forEach(function(u){var v=u.name,d=u.options,k=d===void 0?{}:d,g=u.effect;if(typeof g=="function"){var m=g({state:c,name:v,instance:p,options:k}),w=function(){};l.push(m||w)}})}function y(){l.forEach(function(u){return u()}),l=[]}return p}}gt();var Pi=[hr,yr,pr,fr];gt({defaultModifiers:Pi});var _i=[hr,yr,pr,fr,pi,fi,yi,Kn,ui],ko=gt({defaultModifiers:_i});function j(t,e){Ti(t)&&(t="100%");var r=Mi(t);return t=e===360?t:Math.min(e,Math.max(0,parseFloat(t))),r&&(t=parseInt(String(t*e),10)/100),Math.abs(t-e)<1e-6?1:(e===360?t=(t<0?t%e+e:t%e)/parseFloat(String(e)):t=t%e/parseFloat(String(e)),t)}function je(t){return Math.min(1,Math.max(0,t))}function Ti(t){return typeof t=="string"&&t.indexOf(".")!==-1&&parseFloat(t)===1}function Mi(t){return typeof t=="string"&&t.indexOf("%")!==-1}function mr(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function Re(t){return t<=1?"".concat(Number(t)*100,"%"):t}function re(t){return t.length===1?"0"+t:String(t)}function Ei(t,e,r){return{r:j(t,255)*255,g:j(e,255)*255,b:j(r,255)*255}}function Nt(t,e,r){t=j(t,255),e=j(e,255),r=j(r,255);var n=Math.max(t,e,r),i=Math.min(t,e,r),o=0,a=0,s=(n+i)/2;if(n===i)a=0,o=0;else{var f=n-i;switch(a=s>.5?f/(2-n-i):f/(n+i),n){case t:o=(e-r)/f+(e<r?6:0);break;case e:o=(r-t)/f+2;break;case r:o=(t-e)/f+4;break}o/=6}return{h:o,s:a,l:s}}function Je(t,e,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+(e-t)*(6*r):r<1/2?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function Si(t,e,r){var n,i,o;if(t=j(t,360),e=j(e,100),r=j(r,100),e===0)i=r,o=r,n=r;else{var a=r<.5?r*(1+e):r+e-r*e,s=2*r-a;n=Je(s,a,t+1/3),i=Je(s,a,t),o=Je(s,a,t-1/3)}return{r:n*255,g:i*255,b:o*255}}function It(t,e,r){t=j(t,255),e=j(e,255),r=j(r,255);var n=Math.max(t,e,r),i=Math.min(t,e,r),o=0,a=n,s=n-i,f=n===0?0:s/n;if(n===i)o=0;else{switch(n){case t:o=(e-r)/s+(e<r?6:0);break;case e:o=(r-t)/s+2;break;case r:o=(t-e)/s+4;break}o/=6}return{h:o,s:f,v:a}}function ji(t,e,r){t=j(t,360)*6,e=j(e,100),r=j(r,100);var n=Math.floor(t),i=t-n,o=r*(1-e),a=r*(1-i*e),s=r*(1-(1-i)*e),f=n%6,c=[r,a,o,o,s,r][f],l=[s,r,r,a,o,o][f],b=[o,o,s,r,r,a][f];return{r:c*255,g:l*255,b:b*255}}function Vt(t,e,r,n){var i=[re(Math.round(t).toString(16)),re(Math.round(e).toString(16)),re(Math.round(r).toString(16))];return n&&i[0].startsWith(i[0].charAt(1))&&i[1].startsWith(i[1].charAt(1))&&i[2].startsWith(i[2].charAt(1))?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function Ri(t,e,r,n,i){var o=[re(Math.round(t).toString(16)),re(Math.round(e).toString(16)),re(Math.round(r).toString(16)),re(qi(n))];return i&&o[0].startsWith(o[0].charAt(1))&&o[1].startsWith(o[1].charAt(1))&&o[2].startsWith(o[2].charAt(1))&&o[3].startsWith(o[3].charAt(1))?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0):o.join("")}function qi(t){return Math.round(parseFloat(t)*255).toString(16)}function Ut(t){return D(t)/255}function D(t){return parseInt(t,16)}function Fi(t){return{r:t>>16,g:(t&65280)>>8,b:t&255}}var rt={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function Di(t){var e={r:0,g:0,b:0},r=1,n=null,i=null,o=null,a=!1,s=!1;return typeof t=="string"&&(t=Wi(t)),typeof t=="object"&&(K(t.r)&&K(t.g)&&K(t.b)?(e=Ei(t.r,t.g,t.b),a=!0,s=String(t.r).substr(-1)==="%"?"prgb":"rgb"):K(t.h)&&K(t.s)&&K(t.v)?(n=Re(t.s),i=Re(t.v),e=ji(t.h,n,i),a=!0,s="hsv"):K(t.h)&&K(t.s)&&K(t.l)&&(n=Re(t.s),o=Re(t.l),e=Si(t.h,n,o),a=!0,s="hsl"),Object.prototype.hasOwnProperty.call(t,"a")&&(r=t.a)),r=mr(r),{ok:a,format:t.format||s,r:Math.min(255,Math.max(e.r,0)),g:Math.min(255,Math.max(e.g,0)),b:Math.min(255,Math.max(e.b,0)),a:r}}var Li="[-\\+]?\\d+%?",Ci="[-\\+]?\\d*\\.\\d+%?",z="(?:".concat(Ci,")|(?:").concat(Li,")"),Ze="[\\s|\\(]+(".concat(z,")[,|\\s]+(").concat(z,")[,|\\s]+(").concat(z,")\\s*\\)?"),ze="[\\s|\\(]+(".concat(z,")[,|\\s]+(").concat(z,")[,|\\s]+(").concat(z,")[,|\\s]+(").concat(z,")\\s*\\)?"),N={CSS_UNIT:new RegExp(z),rgb:new RegExp("rgb"+Ze),rgba:new RegExp("rgba"+ze),hsl:new RegExp("hsl"+Ze),hsla:new RegExp("hsla"+ze),hsv:new RegExp("hsv"+Ze),hsva:new RegExp("hsva"+ze),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function Wi(t){if(t=t.trim().toLowerCase(),t.length===0)return!1;var e=!1;if(rt[t])t=rt[t],e=!0;else if(t==="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var r=N.rgb.exec(t);return r?{r:r[1],g:r[2],b:r[3]}:(r=N.rgba.exec(t),r?{r:r[1],g:r[2],b:r[3],a:r[4]}:(r=N.hsl.exec(t),r?{h:r[1],s:r[2],l:r[3]}:(r=N.hsla.exec(t),r?{h:r[1],s:r[2],l:r[3],a:r[4]}:(r=N.hsv.exec(t),r?{h:r[1],s:r[2],v:r[3]}:(r=N.hsva.exec(t),r?{h:r[1],s:r[2],v:r[3],a:r[4]}:(r=N.hex8.exec(t),r?{r:D(r[1]),g:D(r[2]),b:D(r[3]),a:Ut(r[4]),format:e?"name":"hex8"}:(r=N.hex6.exec(t),r?{r:D(r[1]),g:D(r[2]),b:D(r[3]),format:e?"name":"hex"}:(r=N.hex4.exec(t),r?{r:D(r[1]+r[1]),g:D(r[2]+r[2]),b:D(r[3]+r[3]),a:Ut(r[4]+r[4]),format:e?"name":"hex8"}:(r=N.hex3.exec(t),r?{r:D(r[1]+r[1]),g:D(r[2]+r[2]),b:D(r[3]+r[3]),format:e?"name":"hex"}:!1)))))))))}function K(t){return!!N.CSS_UNIT.exec(String(t))}var Oo=function(){function t(e,r){e===void 0&&(e=""),r===void 0&&(r={});var n;if(e instanceof t)return e;typeof e=="number"&&(e=Fi(e)),this.originalInput=e;var i=Di(e);this.originalInput=e,this.r=i.r,this.g=i.g,this.b=i.b,this.a=i.a,this.roundA=Math.round(100*this.a)/100,this.format=(n=r.format)!==null&&n!==void 0?n:i.format,this.gradientType=r.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=i.ok}return t.prototype.isDark=function(){return this.getBrightness()<128},t.prototype.isLight=function(){return!this.isDark()},t.prototype.getBrightness=function(){var e=this.toRgb();return(e.r*299+e.g*587+e.b*114)/1e3},t.prototype.getLuminance=function(){var e=this.toRgb(),r,n,i,o=e.r/255,a=e.g/255,s=e.b/255;return o<=.03928?r=o/12.92:r=Math.pow((o+.055)/1.055,2.4),a<=.03928?n=a/12.92:n=Math.pow((a+.055)/1.055,2.4),s<=.03928?i=s/12.92:i=Math.pow((s+.055)/1.055,2.4),.2126*r+.7152*n+.0722*i},t.prototype.getAlpha=function(){return this.a},t.prototype.setAlpha=function(e){return this.a=mr(e),this.roundA=Math.round(100*this.a)/100,this},t.prototype.isMonochrome=function(){var e=this.toHsl().s;return e===0},t.prototype.toHsv=function(){var e=It(this.r,this.g,this.b);return{h:e.h*360,s:e.s,v:e.v,a:this.a}},t.prototype.toHsvString=function(){var e=It(this.r,this.g,this.b),r=Math.round(e.h*360),n=Math.round(e.s*100),i=Math.round(e.v*100);return this.a===1?"hsv(".concat(r,", ").concat(n,"%, ").concat(i,"%)"):"hsva(".concat(r,", ").concat(n,"%, ").concat(i,"%, ").concat(this.roundA,")")},t.prototype.toHsl=function(){var e=Nt(this.r,this.g,this.b);return{h:e.h*360,s:e.s,l:e.l,a:this.a}},t.prototype.toHslString=function(){var e=Nt(this.r,this.g,this.b),r=Math.round(e.h*360),n=Math.round(e.s*100),i=Math.round(e.l*100);return this.a===1?"hsl(".concat(r,", ").concat(n,"%, ").concat(i,"%)"):"hsla(".concat(r,", ").concat(n,"%, ").concat(i,"%, ").concat(this.roundA,")")},t.prototype.toHex=function(e){return e===void 0&&(e=!1),Vt(this.r,this.g,this.b,e)},t.prototype.toHexString=function(e){return e===void 0&&(e=!1),"#"+this.toHex(e)},t.prototype.toHex8=function(e){return e===void 0&&(e=!1),Ri(this.r,this.g,this.b,this.a,e)},t.prototype.toHex8String=function(e){return e===void 0&&(e=!1),"#"+this.toHex8(e)},t.prototype.toHexShortString=function(e){return e===void 0&&(e=!1),this.a===1?this.toHexString(e):this.toHex8String(e)},t.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},t.prototype.toRgbString=function(){var e=Math.round(this.r),r=Math.round(this.g),n=Math.round(this.b);return this.a===1?"rgb(".concat(e,", ").concat(r,", ").concat(n,")"):"rgba(".concat(e,", ").concat(r,", ").concat(n,", ").concat(this.roundA,")")},t.prototype.toPercentageRgb=function(){var e=function(r){return"".concat(Math.round(j(r,255)*100),"%")};return{r:e(this.r),g:e(this.g),b:e(this.b),a:this.a}},t.prototype.toPercentageRgbString=function(){var e=function(r){return Math.round(j(r,255)*100)};return this.a===1?"rgb(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%)"):"rgba(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%, ").concat(this.roundA,")")},t.prototype.toName=function(){if(this.a===0)return"transparent";if(this.a<1)return!1;for(var e="#"+Vt(this.r,this.g,this.b,!1),r=0,n=Object.entries(rt);r<n.length;r++){var i=n[r],o=i[0],a=i[1];if(e===a)return o}return!1},t.prototype.toString=function(e){var r=!!e;e=e??this.format;var n=!1,i=this.a<1&&this.a>=0,o=!r&&i&&(e.startsWith("hex")||e==="name");return o?e==="name"&&this.a===0?this.toName():this.toRgbString():(e==="rgb"&&(n=this.toRgbString()),e==="prgb"&&(n=this.toPercentageRgbString()),(e==="hex"||e==="hex6")&&(n=this.toHexString()),e==="hex3"&&(n=this.toHexString(!0)),e==="hex4"&&(n=this.toHex8String(!0)),e==="hex8"&&(n=this.toHex8String()),e==="name"&&(n=this.toName()),e==="hsl"&&(n=this.toHslString()),e==="hsv"&&(n=this.toHsvString()),n||this.toHexString())},t.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},t.prototype.clone=function(){return new t(this.toString())},t.prototype.lighten=function(e){e===void 0&&(e=10);var r=this.toHsl();return r.l+=e/100,r.l=je(r.l),new t(r)},t.prototype.brighten=function(e){e===void 0&&(e=10);var r=this.toRgb();return r.r=Math.max(0,Math.min(255,r.r-Math.round(255*-(e/100)))),r.g=Math.max(0,Math.min(255,r.g-Math.round(255*-(e/100)))),r.b=Math.max(0,Math.min(255,r.b-Math.round(255*-(e/100)))),new t(r)},t.prototype.darken=function(e){e===void 0&&(e=10);var r=this.toHsl();return r.l-=e/100,r.l=je(r.l),new t(r)},t.prototype.tint=function(e){return e===void 0&&(e=10),this.mix("white",e)},t.prototype.shade=function(e){return e===void 0&&(e=10),this.mix("black",e)},t.prototype.desaturate=function(e){e===void 0&&(e=10);var r=this.toHsl();return r.s-=e/100,r.s=je(r.s),new t(r)},t.prototype.saturate=function(e){e===void 0&&(e=10);var r=this.toHsl();return r.s+=e/100,r.s=je(r.s),new t(r)},t.prototype.greyscale=function(){return this.desaturate(100)},t.prototype.spin=function(e){var r=this.toHsl(),n=(r.h+e)%360;return r.h=n<0?360+n:n,new t(r)},t.prototype.mix=function(e,r){r===void 0&&(r=50);var n=this.toRgb(),i=new t(e).toRgb(),o=r/100,a={r:(i.r-n.r)*o+n.r,g:(i.g-n.g)*o+n.g,b:(i.b-n.b)*o+n.b,a:(i.a-n.a)*o+n.a};return new t(a)},t.prototype.analogous=function(e,r){e===void 0&&(e=6),r===void 0&&(r=30);var n=this.toHsl(),i=360/r,o=[this];for(n.h=(n.h-(i*e>>1)+720)%360;--e;)n.h=(n.h+i)%360,o.push(new t(n));return o},t.prototype.complement=function(){var e=this.toHsl();return e.h=(e.h+180)%360,new t(e)},t.prototype.monochromatic=function(e){e===void 0&&(e=6);for(var r=this.toHsv(),n=r.h,i=r.s,o=r.v,a=[],s=1/e;e--;)a.push(new t({h:n,s:i,v:o})),o=(o+s)%1;return a},t.prototype.splitcomplement=function(){var e=this.toHsl(),r=e.h;return[this,new t({h:(r+72)%360,s:e.s,l:e.l}),new t({h:(r+216)%360,s:e.s,l:e.l})]},t.prototype.onBackground=function(e){var r=this.toRgb(),n=new t(e).toRgb(),i=r.a+n.a*(1-r.a);return new t({r:(r.r*r.a+n.r*n.a*(1-r.a))/i,g:(r.g*r.a+n.g*n.a*(1-r.a))/i,b:(r.b*r.a+n.b*n.a*(1-r.a))/i,a:i})},t.prototype.triad=function(){return this.polyad(3)},t.prototype.tetrad=function(){return this.polyad(4)},t.prototype.polyad=function(e){for(var r=this.toHsl(),n=r.h,i=[this],o=360/e,a=1;a<e;a++)i.push(new t({h:(n+a*o)%360,s:r.s,l:r.l}));return i},t.prototype.equals=function(e){return this.toRgbString()===new t(e).toRgbString()},t}();function ne(){return ne=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ne.apply(this,arguments)}function $i(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,ke(t,e)}function nt(t){return nt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},nt(t)}function ke(t,e){return ke=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ke(t,e)}function Bi(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Le(t,e,r){return Bi()?Le=Reflect.construct.bind():Le=function(i,o,a){var s=[null];s.push.apply(s,o);var f=Function.bind.apply(i,s),c=new f;return a&&ke(c,a.prototype),c},Le.apply(null,arguments)}function Ni(t){return Function.toString.call(t).indexOf("[native code]")!==-1}function it(t){var e=typeof Map=="function"?new Map:void 0;return it=function(n){if(n===null||!Ni(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(n))return e.get(n);e.set(n,i)}function i(){return Le(n,arguments,nt(this).constructor)}return i.prototype=Object.create(n.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),ke(i,n)},it(t)}var Ii=/%[sdj%]/g,Vi=function(){};function ot(t){if(!t||!t.length)return null;var e={};return t.forEach(function(r){var n=r.field;e[n]=e[n]||[],e[n].push(r)}),e}function L(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var i=0,o=r.length;if(typeof t=="function")return t.apply(null,r);if(typeof t=="string"){var a=t.replace(Ii,function(s){if(s==="%%")return"%";if(i>=o)return s;switch(s){case"%s":return String(r[i++]);case"%d":return Number(r[i++]);case"%j":try{return JSON.stringify(r[i++])}catch{return"[Circular]"}break;default:return s}});return a}return t}function Ui(t){return t==="string"||t==="url"||t==="hex"||t==="email"||t==="date"||t==="pattern"}function E(t,e){return!!(t==null||e==="array"&&Array.isArray(t)&&!t.length||Ui(e)&&typeof t=="string"&&!t)}function Gi(t,e,r){var n=[],i=0,o=t.length;function a(s){n.push.apply(n,s||[]),i++,i===o&&r(n)}t.forEach(function(s){e(s,a)})}function Gt(t,e,r){var n=0,i=t.length;function o(a){if(a&&a.length){r(a);return}var s=n;n=n+1,s<i?e(t[s],o):r([])}o([])}function Ki(t){var e=[];return Object.keys(t).forEach(function(r){e.push.apply(e,t[r]||[])}),e}var Kt=function(t){$i(e,t);function e(r,n){var i;return i=t.call(this,"Async Validation Error")||this,i.errors=r,i.fields=n,i}return e}(it(Error));function Ji(t,e,r,n,i){if(e.first){var o=new Promise(function(p,h){var y=function(d){return n(d),d.length?h(new Kt(d,ot(d))):p(i)},u=Ki(t);Gt(u,r,y)});return o.catch(function(p){return p}),o}var a=e.firstFields===!0?Object.keys(t):e.firstFields||[],s=Object.keys(t),f=s.length,c=0,l=[],b=new Promise(function(p,h){var y=function(v){if(l.push.apply(l,v),c++,c===f)return n(l),l.length?h(new Kt(l,ot(l))):p(i)};s.length||(n(l),p(i)),s.forEach(function(u){var v=t[u];a.indexOf(u)!==-1?Gt(v,r,y):Gi(v,r,y)})});return b.catch(function(p){return p}),b}function Zi(t){return!!(t&&t.message!==void 0)}function zi(t,e){for(var r=t,n=0;n<e.length;n++){if(r==null)return r;r=r[e[n]]}return r}function Jt(t,e){return function(r){var n;return t.fullFields?n=zi(e,t.fullFields):n=e[r.field||t.fullField],Zi(r)?(r.field=r.field||t.fullField,r.fieldValue=n,r):{message:typeof r=="function"?r():r,fieldValue:n,field:r.field||t.fullField}}}function Zt(t,e){if(e){for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];typeof n=="object"&&typeof t[r]=="object"?t[r]=ne({},t[r],n):t[r]=n}}return t}var br=function(e,r,n,i,o,a){e.required&&(!n.hasOwnProperty(e.field)||E(r,a||e.type))&&i.push(L(o.messages.required,e.fullField))},Xi=function(e,r,n,i,o){(/^\s+$/.test(r)||r==="")&&i.push(L(o.messages.whitespace,e.fullField))},qe,Qi=function(){if(qe)return qe;var t="[a-fA-F\\d:]",e=function(m){return m&&m.includeBoundaries?"(?:(?<=\\s|^)(?="+t+")|(?<="+t+")(?=\\s|$))":""},r="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",i=(`
(?:
(?:`+n+":){7}(?:"+n+`|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:`+n+":){6}(?:"+r+"|:"+n+`|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4
(?:`+n+":){5}(?::"+r+"|(?::"+n+`){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4
(?:`+n+":){4}(?:(?::"+n+"){0,1}:"+r+"|(?::"+n+`){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4
(?:`+n+":){3}(?:(?::"+n+"){0,2}:"+r+"|(?::"+n+`){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4
(?:`+n+":){2}(?:(?::"+n+"){0,3}:"+r+"|(?::"+n+`){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4
(?:`+n+":){1}(?:(?::"+n+"){0,4}:"+r+"|(?::"+n+`){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4
(?::(?:(?::`+n+"){0,5}:"+r+"|(?::"+n+`){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`).replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),o=new RegExp("(?:^"+r+"$)|(?:^"+i+"$)"),a=new RegExp("^"+r+"$"),s=new RegExp("^"+i+"$"),f=function(m){return m&&m.exact?o:new RegExp("(?:"+e(m)+r+e(m)+")|(?:"+e(m)+i+e(m)+")","g")};f.v4=function(g){return g&&g.exact?a:new RegExp(""+e(g)+r+e(g),"g")},f.v6=function(g){return g&&g.exact?s:new RegExp(""+e(g)+i+e(g),"g")};var c="(?:(?:[a-z]+:)?//)",l="(?:\\S+(?::\\S*)?@)?",b=f.v4().source,p=f.v6().source,h="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",y="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",u="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",v="(?::\\d{2,5})?",d='(?:[/?#][^\\s"]*)?',k="(?:"+c+"|www\\.)"+l+"(?:localhost|"+b+"|"+p+"|"+h+y+u+")"+v+d;return qe=new RegExp("(?:^"+k+"$)","i"),qe},zt={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},ve={integer:function(e){return ve.number(e)&&parseInt(e,10)===e},float:function(e){return ve.number(e)&&!ve.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch{return!1}},date:function(e){return typeof e.getTime=="function"&&typeof e.getMonth=="function"&&typeof e.getYear=="function"&&!isNaN(e.getTime())},number:function(e){return isNaN(e)?!1:typeof e=="number"},object:function(e){return typeof e=="object"&&!ve.array(e)},method:function(e){return typeof e=="function"},email:function(e){return typeof e=="string"&&e.length<=320&&!!e.match(zt.email)},url:function(e){return typeof e=="string"&&e.length<=2048&&!!e.match(Qi())},hex:function(e){return typeof e=="string"&&!!e.match(zt.hex)}},Yi=function(e,r,n,i,o){if(e.required&&r===void 0){br(e,r,n,i,o);return}var a=["integer","float","array","regexp","object","method","email","number","date","url","hex"],s=e.type;a.indexOf(s)>-1?ve[s](r)||i.push(L(o.messages.types[s],e.fullField,e.type)):s&&typeof r!==e.type&&i.push(L(o.messages.types[s],e.fullField,e.type))},eo=function(e,r,n,i,o){var a=typeof e.len=="number",s=typeof e.min=="number",f=typeof e.max=="number",c=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,l=r,b=null,p=typeof r=="number",h=typeof r=="string",y=Array.isArray(r);if(p?b="number":h?b="string":y&&(b="array"),!b)return!1;y&&(l=r.length),h&&(l=r.replace(c,"_").length),a?l!==e.len&&i.push(L(o.messages[b].len,e.fullField,e.len)):s&&!f&&l<e.min?i.push(L(o.messages[b].min,e.fullField,e.min)):f&&!s&&l>e.max?i.push(L(o.messages[b].max,e.fullField,e.max)):s&&f&&(l<e.min||l>e.max)&&i.push(L(o.messages[b].range,e.fullField,e.min,e.max))},ae="enum",to=function(e,r,n,i,o){e[ae]=Array.isArray(e[ae])?e[ae]:[],e[ae].indexOf(r)===-1&&i.push(L(o.messages[ae],e.fullField,e[ae].join(", ")))},ro=function(e,r,n,i,o){if(e.pattern){if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(r)||i.push(L(o.messages.pattern.mismatch,e.fullField,r,e.pattern));else if(typeof e.pattern=="string"){var a=new RegExp(e.pattern);a.test(r)||i.push(L(o.messages.pattern.mismatch,e.fullField,r,e.pattern))}}},O={required:br,whitespace:Xi,type:Yi,range:eo,enum:to,pattern:ro},no=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r,"string")&&!e.required)return n();O.required(e,r,i,a,o,"string"),E(r,"string")||(O.type(e,r,i,a,o),O.range(e,r,i,a,o),O.pattern(e,r,i,a,o),e.whitespace===!0&&O.whitespace(e,r,i,a,o))}n(a)},io=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o),r!==void 0&&O.type(e,r,i,a,o)}n(a)},oo=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(r===""&&(r=void 0),E(r)&&!e.required)return n();O.required(e,r,i,a,o),r!==void 0&&(O.type(e,r,i,a,o),O.range(e,r,i,a,o))}n(a)},ao=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o),r!==void 0&&O.type(e,r,i,a,o)}n(a)},so=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o),E(r)||O.type(e,r,i,a,o)}n(a)},fo=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o),r!==void 0&&(O.type(e,r,i,a,o),O.range(e,r,i,a,o))}n(a)},co=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o),r!==void 0&&(O.type(e,r,i,a,o),O.range(e,r,i,a,o))}n(a)},uo=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(r==null&&!e.required)return n();O.required(e,r,i,a,o,"array"),r!=null&&(O.type(e,r,i,a,o),O.range(e,r,i,a,o))}n(a)},lo=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o),r!==void 0&&O.type(e,r,i,a,o)}n(a)},po="enum",ho=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o),r!==void 0&&O[po](e,r,i,a,o)}n(a)},go=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r,"string")&&!e.required)return n();O.required(e,r,i,a,o),E(r,"string")||O.pattern(e,r,i,a,o)}n(a)},vo=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r,"date")&&!e.required)return n();if(O.required(e,r,i,a,o),!E(r,"date")){var f;r instanceof Date?f=r:f=new Date(r),O.type(e,f,i,a,o),f&&O.range(e,f.getTime(),i,a,o)}}n(a)},yo=function(e,r,n,i,o){var a=[],s=Array.isArray(r)?"array":typeof r;O.required(e,r,i,a,o,s),n(a)},Xe=function(e,r,n,i,o){var a=e.type,s=[],f=e.required||!e.required&&i.hasOwnProperty(e.field);if(f){if(E(r,a)&&!e.required)return n();O.required(e,r,i,s,o,a),E(r,a)||O.type(e,r,i,s,o)}n(s)},mo=function(e,r,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(E(r)&&!e.required)return n();O.required(e,r,i,a,o)}n(a)},be={string:no,method:io,number:oo,boolean:ao,regexp:so,integer:fo,float:co,array:uo,object:lo,enum:ho,pattern:go,date:vo,url:Xe,hex:Xe,email:Xe,required:yo,any:mo};function at(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var st=at(),Ie=function(){function t(r){this.rules=null,this._messages=st,this.define(r)}var e=t.prototype;return e.define=function(n){var i=this;if(!n)throw new Error("Cannot configure a schema with no rules");if(typeof n!="object"||Array.isArray(n))throw new Error("Rules must be an object");this.rules={},Object.keys(n).forEach(function(o){var a=n[o];i.rules[o]=Array.isArray(a)?a:[a]})},e.messages=function(n){return n&&(this._messages=Zt(at(),n)),this._messages},e.validate=function(n,i,o){var a=this;i===void 0&&(i={}),o===void 0&&(o=function(){});var s=n,f=i,c=o;if(typeof f=="function"&&(c=f,f={}),!this.rules||Object.keys(this.rules).length===0)return c&&c(null,s),Promise.resolve(s);function l(u){var v=[],d={};function k(m){if(Array.isArray(m)){var w;v=(w=v).concat.apply(w,m)}else v.push(m)}for(var g=0;g<u.length;g++)k(u[g]);v.length?(d=ot(v),c(v,d)):c(null,s)}if(f.messages){var b=this.messages();b===st&&(b=at()),Zt(b,f.messages),f.messages=b}else f.messages=this.messages();var p={},h=f.keys||Object.keys(this.rules);h.forEach(function(u){var v=a.rules[u],d=s[u];v.forEach(function(k){var g=k;typeof g.transform=="function"&&(s===n&&(s=ne({},s)),d=s[u]=g.transform(d)),typeof g=="function"?g={validator:g}:g=ne({},g),g.validator=a.getValidationMethod(g),g.validator&&(g.field=u,g.fullField=g.fullField||u,g.type=a.getType(g),p[u]=p[u]||[],p[u].push({rule:g,value:d,source:s,field:u}))})});var y={};return Ji(p,f,function(u,v){var d=u.rule,k=(d.type==="object"||d.type==="array")&&(typeof d.fields=="object"||typeof d.defaultField=="object");k=k&&(d.required||!d.required&&u.value),d.field=u.field;function g(x,H){return ne({},H,{fullField:d.fullField+"."+x,fullFields:d.fullFields?[].concat(d.fullFields,[x]):[x]})}function m(x){x===void 0&&(x=[]);var H=Array.isArray(x)?x:[x];!f.suppressWarning&&H.length&&t.warning("async-validator:",H),H.length&&d.message!==void 0&&(H=[].concat(d.message));var A=H.map(Jt(d,s));if(f.first&&A.length)return y[d.field]=1,v(A);if(!k)v(A);else{if(d.required&&!u.value)return d.message!==void 0?A=[].concat(d.message).map(Jt(d,s)):f.error&&(A=[f.error(d,L(f.messages.required,d.field))]),v(A);var T={};d.defaultField&&Object.keys(u.value).map(function(_){T[_]=d.defaultField}),T=ne({},T,u.rule.fields);var S={};Object.keys(T).forEach(function(_){var P=T[_],Q=Array.isArray(P)?P:[P];S[_]=Q.map(g.bind(null,_))});var M=new t(S);M.messages(f.messages),u.rule.options&&(u.rule.options.messages=f.messages,u.rule.options.error=f.error),M.validate(u.value,u.rule.options||f,function(_){var P=[];A&&A.length&&P.push.apply(P,A),_&&_.length&&P.push.apply(P,_),v(P.length?P:null)})}}var w;if(d.asyncValidator)w=d.asyncValidator(d,u.value,m,u.source,f);else if(d.validator){try{w=d.validator(d,u.value,m,u.source,f)}catch(x){console.error==null||console.error(x),f.suppressValidatorError||setTimeout(function(){throw x},0),m(x.message)}w===!0?m():w===!1?m(typeof d.message=="function"?d.message(d.fullField||d.field):d.message||(d.fullField||d.field)+" fails"):w instanceof Array?m(w):w instanceof Error&&m(w.message)}w&&w.then&&w.then(function(){return m()},function(x){return m(x)})},function(u){l(u)},s)},e.getType=function(n){if(n.type===void 0&&n.pattern instanceof RegExp&&(n.type="pattern"),typeof n.validator!="function"&&n.type&&!be.hasOwnProperty(n.type))throw new Error(L("Unknown rule type %s",n.type));return n.type||"string"},e.getValidationMethod=function(n){if(typeof n.validator=="function")return n.validator;var i=Object.keys(n),o=i.indexOf("message");return o!==-1&&i.splice(o,1),i.length===1&&i[0]==="required"?be.required:be[this.getType(n)]||void 0},t}();Ie.register=function(e,r){if(typeof r!="function")throw new Error("Cannot register a validator by type, validator is not a function");be[e]=r};Ie.warning=Vi;Ie.messages=st;Ie.validators=be;export{sr as E,Ie as S,Oo as T,wo as c,xo as p,An as u,ko as y};
