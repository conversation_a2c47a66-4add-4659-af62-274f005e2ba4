import{u as Y}from"./vue-router-e9iWfNxP.js";import{_ as I,S as ee}from"./index-CkIekciI.js";import{d as L,e as q,f as M,g as K,b as A,E as w}from"./element-plus-DZBbDeaO.js";import{d as B,r as C,Q as D,E as N,G as t,I as e,b as a,$ as k,J as U,o as $,K as j,u as F,D as R,a as Z,F as se,c as H,ax as oe,C as re,H as te}from"./vue-vendor-2E6AJATX.js";import{g as ae,c as E,s as le,r as ne}from"./utils-common-CvYGMv_l.js";import{u as O}from"./app-stores-DSLz6__G.js";import{M as J}from"./Modal-D0LrqBzM.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";import"./element-icons-CT2GCbaF.js";import"./vendor-lodash-BPsl90Nm.js";const ie={class:"forgot-password-link"},de={class:"register-link"},me=B({__name:"PasswordLogin",emits:["switchTab","loginSuccess"],setup(_,{emit:g}){const m=g,y=O(),V=C(!1),S=C(),l=D({userType:"",username:"",password:"",rememberMe:!1}),n={userType:[{required:!0,message:"请选择登录身份",trigger:"change"}],username:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},r=async()=>{if(S.value)try{await S.value.validate(),V.value=!0;const f=await E.login({mobile:l.username,password:l.password});if(f.code===1){const o=f.data;y.login(o),l.rememberMe?(localStorage.setItem("rememberedUsername",l.username),le("rememberedPassword",l.password),localStorage.setItem("rememberedUserType",l.userType)):(localStorage.removeItem("rememberedUsername"),ne("rememberedPassword"),localStorage.removeItem("rememberedUserType")),w.success("登录成功"),m("loginSuccess")}else w.error(f.msg||"登录失败，请检查用户名和密码")}catch(f){console.error("登录失败:",f),w.error("登录失败，请检查用户名和密码")}finally{V.value=!1}},c=()=>{m("switchTab","forgot")},d=()=>{m("switchTab","register")},T=localStorage.getItem("rememberedUsername"),v=ae("rememberedPassword"),x=localStorage.getItem("rememberedUserType");return T&&v&&x&&(l.username=T,l.password=v,l.userType=x,l.rememberMe=!0),(f,o)=>{const u=q,p=M,P=K,b=A,s=L;return $(),N(s,{ref_key:"passwordFormRef",ref:S,model:l,rules:n,"validate-on-rule-change":!1,class:"login-form"},{default:t(()=>[e(p,{prop:"username"},{default:t(()=>[e(u,{modelValue:l.username,"onUpdate:modelValue":o[0]||(o[0]=i=>l.username=i),placeholder:"请输入手机号",size:"large",class:"login-input"},null,8,["modelValue"])]),_:1}),e(p,{prop:"password",class:"password"},{default:t(()=>[e(u,{modelValue:l.password,"onUpdate:modelValue":o[1]||(o[1]=i=>l.password=i),type:"password",placeholder:"请输入登录密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),e(p,{class:"form-options"},{default:t(()=>[e(P,{modelValue:l.rememberMe,"onUpdate:modelValue":o[2]||(o[2]=i=>l.rememberMe=i),class:"remember-checkbox"},{default:t(()=>o[3]||(o[3]=[k(" 记住密码 ")])),_:1,__:[3]},8,["modelValue"]),a("div",ie,[a("a",{href:"#",onClick:U(c,["prevent"])},"忘记密码")])]),_:1}),e(p,null,{default:t(()=>[e(b,{type:"primary",size:"large",class:"login-button",loading:V.value,onClick:r},{default:t(()=>o[4]||(o[4]=[k(" 登录 ")])),_:1,__:[4]},8,["loading"])]),_:1}),a("div",de,[o[5]||(o[5]=a("span",null,"还没有账号？",-1)),a("a",{href:"#",onClick:U(d,["prevent"])},"立即注册")])]),_:1},8,["model"])}}}),ue=I(me,[["__scopeId","data-v-ae60c0a4"]]);function G(){const _=C(0);let g=null;const m=async n=>{if(!n)return w.warning("请先输入手机号"),!1;if(!/^1[3-9]\d{9}$/.test(n))return w.warning("请输入正确的手机号"),!1;if(_.value>0)return w.warning("请等待倒计时结束后再发送"),!1;try{const r=await E.sendSms({mobile:n});return r.code===1?(y(),w.success("验证码已发送"),!0):(w.error(r.msg||"发送验证码失败"),!1)}catch(r){return console.error("发送验证码失败:",r),w.error("发送验证码失败，请重试"),!1}},y=(n=60)=>{g&&clearInterval(g),_.value=n,g=setInterval(()=>{_.value--,_.value<=0&&(clearInterval(g),g=null)},1e3)};return{smsCountdown:_,sendSms:m,startCountdown:y,clearCountdown:()=>{g&&(clearInterval(g),g=null),_.value=0},getSmsButtonText:()=>_.value>0?`${_.value}s后重发`:"获取验证码",isSmsButtonDisabled:()=>_.value>0}}const pe={class:"register-link"},ce=B({__name:"SmsLogin",emits:["switchTab","loginSuccess"],setup(_,{emit:g}){const m=g,y=O(),{sendSms:V,getSmsButtonText:S,isSmsButtonDisabled:l}=G(),n=C(!1),r=C(),c=D({userType:"",phone:"",smsCode:""}),d={userType:[{required:!0,message:"请选择登录身份",trigger:"change"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{4}$/,message:"验证码为4位数字",trigger:"blur"}]},T=async()=>{if(r.value)try{await r.value.validateField("phone"),await V(c.phone)}catch{}},v=async()=>{if(r.value)try{await r.value.validate(),n.value=!0;const f=await E.smsLogin({mobile:c.phone,yzcode:c.smsCode});if(f.code===1){const o=f.data;y.login(o),w.success("登录成功"),m("loginSuccess")}else w.error(f.msg||"登录失败，请检查手机号和验证码")}catch(f){console.error("验证码登录失败:",f),w.error("登录失败，请检查手机号和验证码")}finally{n.value=!1}},x=()=>{m("switchTab","register")};return(f,o)=>{const u=q,p=M,P=A,b=L;return $(),N(b,{ref_key:"smsFormRef",ref:r,model:c,rules:d,"validate-on-rule-change":!1,class:"login-form"},{default:t(()=>[e(p,{prop:"phone"},{default:t(()=>[e(u,{modelValue:c.phone,"onUpdate:modelValue":o[0]||(o[0]=s=>c.phone=s),placeholder:"请输入手机号",size:"large",class:"login-input",maxlength:"11"},null,8,["modelValue"])]),_:1}),e(p,{prop:"smsCode",class:"password"},{default:t(()=>[e(u,{modelValue:c.smsCode,"onUpdate:modelValue":o[1]||(o[1]=s=>c.smsCode=s),placeholder:"请输入验证码",size:"large",class:"login-input sms-input-with-button",maxlength:"6"},{suffix:t(()=>[a("span",{class:R(["sms-text-button",{disabled:F(l)()}]),onClick:T},j(F(S)()),3)]),_:1},8,["modelValue"])]),_:1}),e(p,{class:"form-options"},{default:t(()=>o[2]||(o[2]=[a("div",{class:"sms-tips"},[a("span",null,"验证即登录，未注册将自动创建账号")],-1)])),_:1,__:[2]}),e(p,null,{default:t(()=>[e(P,{type:"primary",size:"large",class:"login-button",loading:n.value,onClick:v},{default:t(()=>o[3]||(o[3]=[k(" 登录 ")])),_:1,__:[3]},8,["loading"])]),_:1}),a("div",pe,[o[4]||(o[4]=a("span",null,"还没有账号？",-1)),a("a",{href:"#",onClick:U(x,["prevent"])},"立即注册")])]),_:1},8,["model"])}}}),ge=I(ce,[["__scopeId","data-v-c918e4a6"]]),fe={class:"login-link"},we=B({__name:"Register",emits:["switchTab","registerSuccess"],setup(_,{emit:g}){const m=g,{sendSms:y,getSmsButtonText:V,isSmsButtonDisabled:S}=G(),l=C(!1),n=C(!1),r=C(!1),c=C(),d=D({phone:"",smsCode:"",password:"",confirmPassword:"",agreeTerms:!1}),x={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{4}$/,message:"验证码为4位数字",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度为6-20位",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,validator:(b,s,i)=>{s===""?i(new Error("请再次输入密码")):s!==d.password?i(new Error("两次输入的密码不一致")):i()},trigger:"blur"}],agreeTerms:[{required:!0,validator:(b,s,i)=>{s?i():i(new Error("请先同意用户协议和隐私政策"))},trigger:"change"}]},f=async()=>{if(c.value)try{await c.value.validateField("phone"),await y(d.phone)}catch{console.log("手机号验证失败")}},o=async()=>{if(c.value)try{await c.value.validate(),l.value=!0;const b=await E.register({mobile:d.phone,password:d.password,yzcode:d.smsCode});b.code===1?(w.success("注册成功，请登录"),m("registerSuccess"),m("switchTab","password")):w.error(b.msg||"注册失败，请重试")}catch(b){console.error("注册失败:",b),w.error("注册失败，请重试")}finally{l.value=!1}},u=()=>{n.value=!0},p=()=>{r.value=!0},P=()=>{m("switchTab","password")};return(b,s)=>{const i=q,z=M,Q=K,W=A,X=L;return $(),Z(se,null,[e(X,{ref_key:"registerFormRef",ref:c,model:d,rules:x,"validate-on-rule-change":!1,class:"login-form"},{default:t(()=>[e(z,{prop:"phone"},{default:t(()=>[e(i,{modelValue:d.phone,"onUpdate:modelValue":s[0]||(s[0]=h=>d.phone=h),placeholder:"请输入手机号",size:"large",class:"login-input",maxlength:"11"},null,8,["modelValue"])]),_:1}),e(z,{prop:"smsCode"},{default:t(()=>[e(i,{modelValue:d.smsCode,"onUpdate:modelValue":s[1]||(s[1]=h=>d.smsCode=h),placeholder:"请输入验证码",size:"large",class:"login-input sms-input-with-button",maxlength:"6"},{suffix:t(()=>[a("span",{class:R(["sms-text-button",{disabled:F(S)()}]),onClick:f},j(F(V)()),3)]),_:1},8,["modelValue"])]),_:1}),e(z,{prop:"password"},{default:t(()=>[e(i,{modelValue:d.password,"onUpdate:modelValue":s[2]||(s[2]=h=>d.password=h),type:"password",placeholder:"请输入密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),e(z,{prop:"confirmPassword"},{default:t(()=>[e(i,{modelValue:d.confirmPassword,"onUpdate:modelValue":s[3]||(s[3]=h=>d.confirmPassword=h),type:"password",placeholder:"请再次输入密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),e(z,{prop:"agreeTerms",class:"agreement-item"},{default:t(()=>[e(Q,{modelValue:d.agreeTerms,"onUpdate:modelValue":s[4]||(s[4]=h=>d.agreeTerms=h),class:"agreement-checkbox"},{default:t(()=>[s[7]||(s[7]=k(" 我已阅读并同意 ")),a("a",{href:"#",class:"agreement-link",onClick:U(u,["prevent"])}," 《平台服务协议》 "),s[8]||(s[8]=k(" 和 ")),a("a",{href:"#",class:"agreement-link",onClick:U(p,["prevent"])}," 《隐私政策》 ")]),_:1,__:[7,8]},8,["modelValue"])]),_:1}),e(z,null,{default:t(()=>[e(W,{type:"primary",size:"large",class:"login-button",loading:l.value,onClick:o},{default:t(()=>s[9]||(s[9]=[k(" 立即注册 ")])),_:1,__:[9]},8,["loading"])]),_:1}),a("div",fe,[s[10]||(s[10]=a("span",null,"已有账号？",-1)),a("a",{href:"#",onClick:U(P,["prevent"])},"立即登录")])]),_:1},8,["model"]),e(J,{modelValue:n.value,"onUpdate:modelValue":s[5]||(s[5]=h=>n.value=h),title:"平台服务协议",width:"600px","show-footer":!1},{default:t(()=>s[11]||(s[11]=[a("div",{class:"agreement-content"},[a("p",null,"这里是平台服务协议的内容，后续会添加具体条款...")],-1)])),_:1,__:[11]},8,["modelValue"]),e(J,{modelValue:r.value,"onUpdate:modelValue":s[6]||(s[6]=h=>r.value=h),title:"隐私政策",width:"600px","show-footer":!1},{default:t(()=>s[12]||(s[12]=[a("div",{class:"agreement-content"},[a("p",null,"这里是隐私政策的内容，后续会添加具体条款...")],-1)])),_:1,__:[12]},8,["modelValue"])],64)}}}),_e=I(we,[["__scopeId","data-v-6e8381c6"]]),ve={class:"login-link"},be=B({__name:"ForgotPassword",emits:["switchTab","resetSuccess"],setup(_,{emit:g}){const m=g,{sendSms:y,getSmsButtonText:V,isSmsButtonDisabled:S}=G(),l=C(!1),n=C(),r=D({phone:"",smsCode:"",newPassword:"",confirmPassword:""}),d={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{4}$/,message:"验证码为4位数字",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,max:20,message:"密码长度为6-20位",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,validator:(o,u,p)=>{u===""?p(new Error("请再次输入新密码")):u!==r.newPassword?p(new Error("两次输入的密码不一致")):p()},trigger:"blur"}]},T=async()=>{if(n.value)try{await n.value.validateField("phone"),await y(r.phone)}catch{console.log("手机号验证失败")}},v=async()=>{if(n.value)try{await n.value.validate(),l.value=!0;const o=await E.resetPassword({mobile:r.phone,yzcode:r.smsCode,password:r.newPassword});o.code===1?(w.success("密码重置成功，请使用新密码登录"),m("resetSuccess"),m("switchTab","password"),f()):w.error(o.msg||"密码重置失败，请重试")}catch(o){console.error("重置密码失败:",o),w.error("密码重置失败，请重试")}finally{l.value=!1}},x=()=>{m("switchTab","password")},f=()=>{n.value&&n.value.resetFields(),Object.assign(r,{phone:"",smsCode:"",newPassword:"",confirmPassword:""})};return(o,u)=>{const p=q,P=M,b=A,s=L;return $(),N(s,{ref_key:"forgotFormRef",ref:n,model:r,rules:d,"validate-on-rule-change":!1,class:"login-form"},{default:t(()=>[e(P,{prop:"phone"},{default:t(()=>[e(p,{modelValue:r.phone,"onUpdate:modelValue":u[0]||(u[0]=i=>r.phone=i),placeholder:"请输入手机号",size:"large",class:"login-input",maxlength:"11"},null,8,["modelValue"])]),_:1}),e(P,{prop:"smsCode",class:"password"},{default:t(()=>[e(p,{modelValue:r.smsCode,"onUpdate:modelValue":u[1]||(u[1]=i=>r.smsCode=i),placeholder:"请输入验证码",size:"large",class:"login-input sms-input-with-button",maxlength:"6"},{suffix:t(()=>[a("span",{class:R(["sms-text-button",{disabled:F(S)()}]),onClick:T},j(F(V)()),3)]),_:1},8,["modelValue"])]),_:1}),e(P,{prop:"newPassword"},{default:t(()=>[e(p,{modelValue:r.newPassword,"onUpdate:modelValue":u[2]||(u[2]=i=>r.newPassword=i),type:"password",placeholder:"请输入新密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),e(P,{prop:"confirmPassword"},{default:t(()=>[e(p,{modelValue:r.confirmPassword,"onUpdate:modelValue":u[3]||(u[3]=i=>r.confirmPassword=i),type:"password",placeholder:"请再次输入新密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),e(P,null,{default:t(()=>[e(b,{type:"primary",size:"large",class:"login-button",loading:l.value,onClick:v},{default:t(()=>u[4]||(u[4]=[k(" 重置密码 ")])),_:1,__:[4]},8,["loading"])]),_:1}),a("div",ve,[u[5]||(u[5]=a("span",null,"想起密码了？",-1)),a("a",{href:"#",onClick:U(x,["prevent"])},"返回登录")])]),_:1},8,["model"])}}}),he=I(be,[["__scopeId","data-v-5b2f41e1"]]),ye={class:"login-container"},Ve={class:"login-right"},Se={class:"login-form-container"},Ce={class:"form-logo"},Te={class:"welcome-title"},Pe={key:0,class:"tab-container"},xe=B({__name:"index",setup(_){const g=Y(),m=C("password"),y={password:ue,sms:ge,register:_e,forgot:he},V=H(()=>y[m.value]),S=H(()=>["password","sms"].includes(m.value)),l=()=>({password:"欢迎登陆",sms:"欢迎登陆",register:"用户注册",forgot:"重置密码"})[m.value]||"欢迎登陆",n=T=>{m.value=T},r=()=>{g.push("/")},c=()=>{n("password")},d=()=>{n("password")};return(T,v)=>($(),Z("div",ye,[v[2]||(v[2]=oe('<div class="login-left" data-v-5165fe24><div class="logo-3d-wrapper" data-v-5165fe24><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1_1754963030315.png" alt="" class="logo-3d-image-1" data-v-5165fe24><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/2_1754963119472.png" alt="" class="logo-3d-image-2" data-v-5165fe24><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/3_1754963135630.png" alt="" class="logo-3d-image-3" data-v-5165fe24></div></div>',1)),a("div",Ve,[a("div",Se,[a("div",Ce,[e(ee,{iconName:"login-logo",className:"logo-icon"})]),a("h2",Te,j(l()),1),S.value?($(),Z("div",Pe,[a("div",{class:R(["tab-item",{active:m.value==="password"}]),onClick:v[0]||(v[0]=x=>n("password"))}," 账号密码登录 ",2),a("div",{class:R(["tab-item",{active:m.value==="sms"}]),onClick:v[1]||(v[1]=x=>n("sms"))}," 验证码登录 ",2)])):re("",!0),($(),N(te(V.value),{onSwitchTab:n,onLoginSuccess:r,onRegisterSuccess:c,onResetSuccess:d},null,32))])])]))}}),Ge=I(xe,[["__scopeId","data-v-5165fe24"]]);export{Ge as default};
