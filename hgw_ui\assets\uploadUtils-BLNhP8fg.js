import{a as d}from"./app-assets-WDvSAEaN.js";import{h as u}from"./utils-common-CvYGMv_l.js";const f=()=>{const{province_list:i,city_list:e,county_list:s}=d.area,a=[];for(const[c,p]of Object.entries(i)){const r={value:c,label:p,children:[]};for(const[o,n]of Object.entries(e))if(o.startsWith(c.substring(0,2))){const t={value:o,label:n,children:[]};for(const[l,h]of Object.entries(s))l.startsWith(o.substring(0,4))&&t.children.push({value:l,label:h});t.children.length>0&&r.children.push(t)}r.children.length>0&&a.push(r)}return a},y=(i,e,s)=>{const{province_list:a,city_list:c,county_list:p}=d.area,r=a[i],o=c[e],n=p[s];return!r||!o||!n?null:[r,o,n]},w=async i=>{try{const e=await u.getOssPolicy();if(e.code!==1)throw new Error("获取OSS签名失败");const{policy:s,signature:a,accessid:c,dir:p,host:r,expire:o}=e.data,n=`${p}${Date.now()}_${i.name}`,t=new FormData;if(t.append("key",n),t.append("policy",s),t.append("OSSAccessKeyId",c),t.append("host",r),t.append("expire",o),t.append("signature",a),t.append("file",i),(await fetch("https://huigupaimai.oss-cn-beijing.aliyuncs.com/",{method:"POST",body:t})).ok)return`${n}`;throw new Error("文件上传失败")}catch(e){throw console.error("上传失败:",e),e}},b=(i,e=5*1024*1024,s=["image/gif","image/png","image/jpeg","image/jpg","image/bmp"])=>{if(i.size>e)throw new Error(`文件大小不能超过${Math.round(e/1024/1024)}MB`);if(!s.includes(i.type))throw new Error("文件类型不支持，请上传gif、png、jpg、bmp格式的图片");return!0};export{y as g,f as i,w as u,b as v};
