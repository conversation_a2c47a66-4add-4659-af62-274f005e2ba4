import{p as H,l as R,E as y,j as q}from"./element-plus-DZBbDeaO.js";import{d as V,r as l,t as O,s as Z,x as $,a as c,b as n,C,I as s,F as B,a1 as A,u,E as _,O as J,P as X,K as D,J as G,X as Q,q as Y,o as r}from"./vue-vendor-2E6AJATX.js";import{a as K,u as ee}from"./vue-router-e9iWfNxP.js";import{P as te}from"./PropertyCard-zkRWinEN.js";import{C as oe}from"./Carousel-CaEHbRFW.js";import{S as h,_ as ae}from"./index-CkIekciI.js";import{n as se}from"./utils-common-CvYGMv_l.js";import{a as ne}from"./app-stores-DSLz6__G.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";const re={class:"bidding"},ie={class:"carousel-container"},ce={class:"section"},le={class:"section-title"},ue={class:"title-container"},de={class:"content-container"},pe={key:0,class:"loading-container"},me={key:1,class:"auction-cards"},fe={key:2,class:"pagination-container"},ge={key:3,class:"empty-state"},ve={key:0,class:"freedom-login-float"},ye=["src"],_e={class:"float-user-info-section"},he={class:"float-user-info-avatar"},we=["src"],ke={class:"float-user-info-content"},Ie={class:"float-user-info-name"},Ce={class:"float-user-info-phone"},Ne={class:"float-menu-list-section"},Ue=V({__name:"FreedomHome",setup(Pe){const w=ee(),N=K(),i=ne(),k=l(""),d=l(1),U=l(8),f=l(0),p=l(!1),E=l([{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/free-banner_1754963528553.jpg",title:"",price:"",description:""},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",title:"推广banner图展示位"},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",title:"推广banner图展示位"}]),m=l([]),g=l(!1),I=async(a=d.value)=>{try{p.value=!0;const o=await se.getTradeZonePageList({pageNo:a,pageSize:U.value,queryHgySupplyDemandDto:{infoTitle:k.value}});if(o.success||o.code===200){const e=o.result;e&&e.records?(m.value=e.records.map(t=>({productId:t.id,productName:t.infoTitle,productImage:t.attachment,currentPrice:t.price,priceUnit:t.priceUnit||"元",viewCount:t.viewNum||0,enterpriseLogo:t.enterpriseLogo||"",enterpriseName:t.enterpriseName||t.company,enterpriseType:t.enterpriseType||"企业",status:t.type,statusName:t.type_dictText,productCount:t.quantity||1,productCountUnit:t.unit||"批",productWeight:t.productWeight||0,productWeightUnit:t.productWeightUnit||"吨",productPower:t.viewNum,productPowerUnit:t.productPowerUnit})),f.value=e.total||0,d.value=a):(m.value=[],f.value=0)}else y.error(o.message||o.msg||"获取数据失败")}catch(o){console.error("获取自由交易区列表失败:",o),y.error("获取数据失败，请稍后重试")}finally{p.value=!1}},j=async a=>{if(!i.checkFreedomLogin())try{await q.confirm("访问资产详情需要登录自由交易账号，是否前往登录？","需要登录",{confirmButtonText:"去登录",cancelButtonText:"取消",type:"warning"});const t=w.currentRoute.value.fullPath;w.push({name:"freedomLogin",query:{redirect:`/propertyDetail?id=${a.productId}&crumbsTitle=自由交易&type=4`}});return}catch{return}const o=m.value.find(t=>t.productId===a.productId),e=(o==null?void 0:o.status)||"4";w.push({name:"propertyDetail",query:{id:a.productId,crumbsTitle:"自由交易",type:e}})},z=a=>{d.value=a,I(a)},P=a=>{d.value=1,I(1),y.success(`正在搜索: ${a}`)},M=()=>{g.value=!g.value},S=()=>{g.value=!1},T=a=>{const o=a.target,e=document.querySelector(".freedom-user-dropdown"),t=document.querySelector(".float-avatar");e&&t&&!e.contains(o)&&!t.contains(o)&&S()},W=async()=>{try{await q.confirm("确定要退出自由交易登录吗？","退出登录",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await i.freedomLogout(),S(),y.success("已退出自由交易登录")}catch{}};return O(()=>N.query.keyword,a=>{a&&typeof a=="string"&&(k.value=a,P(a))},{immediate:!0}),Z(()=>{i.initFreedomUserState(),I(1);const a=N.query.keyword;a&&typeof a=="string"&&(k.value=a,P(a)),document.addEventListener("click",T)}),$(()=>{document.removeEventListener("click",T)}),(a,o)=>{var x,F,L,b;const e=H,t=R;return r(),c(B,null,[n("div",re,[n("div",ie,[s(oe,{items:E.value,autoplay:!0,interval:5e3},null,8,["items"])]),n("div",ce,[n("div",le,[n("div",ue,[s(h,{iconName:"freedom-assets-disposal",className:"title-icon"}),o[2]||(o[2]=n("span",null,"资产处置",-1))])]),n("div",de,[p.value?(r(),c("div",pe,[s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""}),s(e,{rows:2,animated:""})])):(r(),c("div",me,[(r(!0),c(B,null,A(m.value,v=>(r(),_(te,Y({key:v.productId},{ref_for:!0},v,{onClick:j}),null,16))),128))])),!p.value&&f.value>0?(r(),c("div",fe,[s(t,{"current-page":d.value,"onUpdate:currentPage":o[0]||(o[0]=v=>d.value=v),"page-size":U.value,total:f.value,layout:"total, prev, pager, next, jumper",onCurrentChange:z},null,8,["current-page","page-size","total"])])):C("",!0),!p.value&&m.value.length===0?(r(),c("div",ge,o[3]||(o[3]=[n("div",{class:"empty-text"},"暂无数据",-1)]))):C("",!0)])])]),u(i).isFreedomLoggedIn?(r(),c("div",ve,[n("div",{class:"float-avatar",onClick:M},[(x=u(i).freedomUserInfo)!=null&&x.userInfo.avatar?(r(),c("img",{key:0,src:u(i).freedomUserInfo.userInfo.avatar,alt:"用户头像",class:"float-user-avatar-img"},null,8,ye)):(r(),_(h,{key:1,iconName:"user",className:"float-avatar-icon"}))]),(r(),_(Q,{to:"body"},[J(n("div",{class:"freedom-user-dropdown",onClick:o[1]||(o[1]=G(()=>{},["stop"]))},[n("div",_e,[n("div",he,[(F=u(i).freedomUserInfo)!=null&&F.userInfo.avatar?(r(),c("img",{key:0,src:u(i).freedomUserInfo.userInfo.avatar,alt:"用户头像",class:"float-dropdown-avatar-img"},null,8,we)):(r(),_(h,{key:1,iconName:"user",className:"float-info-avatar-icon"}))]),n("div",ke,[n("span",Ie,D(((L=u(i).freedomUserInfo)==null?void 0:L.userInfo.username)||"自由交易用户"),1),n("span",Ce,D(((b=u(i).freedomUserInfo)==null?void 0:b.userInfo.phone)||""),1)])]),n("div",Ne,[n("div",{class:"float-menu-item float-logout-item",onClick:W},[s(h,{iconName:"login-menu-logout",className:"float-menu-icon float-logout-icon"}),o[4]||(o[4]=n("span",null,"退出登录",-1))])])],512),[[X,g.value]])]))])):C("",!0)],64)}}}),Ze=ae(Ue,[["__scopeId","data-v-76dfcac0"]]);export{Ze as default};
