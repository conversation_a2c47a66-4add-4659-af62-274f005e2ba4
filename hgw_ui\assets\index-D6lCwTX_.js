import"./element-plus-DZBbDeaO.js";import{d as t,a as e,b as r,I as i,a7 as m,o as p}from"./vue-vendor-2E6AJATX.js";import{_ as s}from"./index-CkIekciI.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vue-router-e9iWfNxP.js";import"./utils-common-CvYGMv_l.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";import"./app-stores-DSLz6__G.js";const a={class:"bidding"},n={class:"bidding-main"},c=t({__name:"index",setup(_){return(d,f)=>{const o=m("router-view");return p(),e("div",a,[r("main",n,[i(o)])])}}}),q=s(c,[["__scopeId","data-v-e08a3c3f"]]);export{q as default};
