import{b as e,E as a,v as t}from"./element-plus-BiAL0NdQ.js";import{S as n,_ as s}from"./index-CCmno-0X.js";import{a as i,u as r}from"./vue-router-C0lzQS1p.js";import{b as o,o as l}from"./utils-common-PdkFOSu3.js";import{d,r as u,c,s as m,t as v,a as p,b as y,O as j,K as b,I as f,G as g,$ as h,o as _}from"./vue-vendor-D6tHD5lA.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./app-stores-CLUCXxRF.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";const k={class:"announcement-detail-container"},S={class:"breadcrumb"},w={class:"current"},q={key:0,class:"announcement-content"},D={class:"announcement-title"},N={class:"announcement-meta"},T={class:"publish-time"},$=["innerHTML"],x={key:1,class:"loading-container","element-loading-text":"加载中..."},A={key:2,class:"error-container"},M={class:"error-content"},C=s(d({__name:"AnnouncementDetail",setup(s){const d=i(),C=r(),H=u(null),I=u([]),L=u(!0),E=u(!1),F=c(()=>d.query.id||d.params.id),G=c(()=>d.query.crumbsTitle||"公告信息"),K=async()=>{if(!F.value)return E.value=!0,void(L.value=!1);try{L.value=!0,E.value=!1;const e=d.query.type;let t;if("auction"===e){if(t=await o.getAuctionSessionAnnouncement({id:F.value}),1===t.code&&t.data){const e=t.data;H.value={id:Number(F.value),title:e.pmh_name||"",content:e.pmh_gonggao||"",addtime:e.addtime?O(e.addtime):"",cate_name:"拍卖会公告"}}}else if(t=await l.getTenderDetail({id:F.value}),1===t.code&&t.data){const a=t.data;H.value={id:Number(F.value),title:a.title||"",content:a.content||"",addtime:a.addtime||"",cate_name:"purchase"===e?"采购公告":"销售公告"}}H.value?await Y():(E.value=!0,a.error("公告不存在或已被删除"))}catch(e){E.value=!0,a.error("获取公告详情失败，请稍后重试")}finally{L.value=!1}},O=e=>{const a=new Date(Number(e));return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`},Y=async()=>{try{I.value=[{id:2,title:"关于调整拍卖保证金比例的通知",addtime:"2024-03-10 14:20:00"},{id:3,title:"2024年春季拍卖会征集公告",addtime:"2024-03-05 09:15:00"},{id:4,title:"拍卖规则更新说明",addtime:"2024-02-28 16:45:00"}]}catch(e){}},z=()=>{C.back()},B=()=>{C.go(-1)};return m(()=>{K()}),v(()=>d.query.id,e=>{e&&K()}),(a,s)=>{var i;const r=n,o=e,l=t;return _(),p("div",k,[y("div",S,[y("span",{class:"breadcrumb-title",onClick:z},b(G.value),1),s[0]||(s[0]=y("span",{class:"separator"},">",-1)),y("span",w,b((null==(i=H.value)?void 0:i.title)||"公告详情"),1)]),H.value?(_(),p("div",q,[y("span",D,b(H.value.title),1),y("div",N,[y("span",T,"发布时间："+b(H.value.addtime),1)]),y("div",{class:"announcement-body",innerHTML:H.value.content},null,8,$)])):L.value?j((_(),p("div",x,null,512)),[[l,!0]]):(_(),p("div",A,[y("div",M,[f(r,{iconName:"warning",class:"error-icon"}),s[2]||(s[2]=y("h3",null,"公告不存在或已被删除",-1)),s[3]||(s[3]=y("p",null,"您访问的公告可能已被删除或不存在，请检查链接是否正确。",-1)),f(o,{type:"primary",onClick:B},{default:g(()=>s[1]||(s[1]=[h("返回上一页")])),_:1,__:[1]})])]))])}}}),[["__scopeId","data-v-a50f12d6"]]);export{C as default};
