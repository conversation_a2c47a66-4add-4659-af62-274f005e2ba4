import{d as t,a as e,b as r,I as n,a7 as a,o as m}from"./vue-vendor-2E6AJATX.js";import{_ as i}from"./index-CkIekciI.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vue-router-e9iWfNxP.js";import"./utils-common-CvYGMv_l.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./element-plus-DZBbDeaO.js";import"./element-icons-CT2GCbaF.js";import"./vendor-lodash-BPsl90Nm.js";import"./crypto-vendor-SDRNRQV4.js";import"./app-stores-DSLz6__G.js";const p={class:"announcement"},s={class:"announcement-main"},c=t({__name:"index",setup(_){return(d,u)=>{const o=a("router-view");return m(),e("div",p,[r("main",s,[n(o)])])}}}),q=i(c,[["__scopeId","data-v-de758aa8"]]);export{q as default};
