import{u as C}from"./vue-router-e9iWfNxP.js";import{u as I,S as r,A as _,_ as N}from"./index-CkIekciI.js";import{C as x}from"./Carousel-CaEHbRFW.js";import{d as B,r as j,s as A,a as o,b as t,I as l,u as n,F as v,a1 as h,E as k,q as f,o as i}from"./vue-vendor-2E6AJATX.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./utils-common-CvYGMv_l.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./element-plus-DZBbDeaO.js";import"./element-icons-CT2GCbaF.js";import"./vendor-lodash-BPsl90Nm.js";import"./crypto-vendor-SDRNRQV4.js";import"./app-stores-DSLz6__G.js";const q={class:"bidding"},w={class:"carousel-container"},E={class:"section"},F={class:"section-title"},H={class:"title-container"},M={class:"content-container"},S={key:0,class:"loading"},V={key:1,class:"auction-cards"},$={key:2,class:"placeholder"},D={class:"section"},L={class:"section-title"},P={class:"title-container"},R={class:"content-container"},T={key:0,class:"loading"},z={key:1,class:"auction-cards"},G={key:2,class:"placeholder"},J=B({__name:"BiddingHome",setup(K){const d=C(),{auctionItems:c,accomplishItems:m,loading:u,initAuctions:y}=I(),b=j([{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/banner-bidding_1754963452117.jpg",title:""},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",title:"推广banner图展示位"},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",title:"推广banner图展示位"}]);A(async()=>{await y()});const p=a=>{d.push({name:"auctionDetail",query:{id:a.productId,pmhId:a.pmhId,crumbsTitle:"竞价交易"}})},g=(a,s)=>{d.push({name:"biddingInfo",query:{name:a,type:s}})};return(a,s)=>(i(),o("div",q,[t("div",w,[l(x,{items:b.value,autoplay:!0,interval:5e3},null,8,["items"])]),t("div",E,[t("div",F,[t("div",H,[l(r,{iconName:"bidding-recommend",className:"title-icon"}),s[2]||(s[2]=t("span",null,"推荐标的",-1))]),t("div",{class:"more-container",onClick:s[0]||(s[0]=e=>g("",""))},[s[3]||(s[3]=t("span",null,"更多",-1)),l(r,{iconName:"auction-arrows-right",className:"more-icon"})])]),t("div",M,[n(u)?(i(),o("div",S,s[4]||(s[4]=[t("div",{class:"loading-text"},"加载中...",-1)]))):n(c).length>0?(i(),o("div",V,[(i(!0),o(v,null,h(n(c),e=>(i(),k(_,f({key:e.id},{ref_for:!0},e,{onClick:p}),null,16))),128))])):(i(),o("div",$,s[5]||(s[5]=[t("div",{class:"placeholder-text"},"暂无推荐标的",-1)])))])]),t("div",D,[t("div",L,[t("div",P,[l(r,{iconName:"bidding-case",className:"title-icon"}),s[6]||(s[6]=t("p",null,"成交案例",-1))]),t("div",{class:"more-container",onClick:s[1]||(s[1]=e=>g("status","completed"))},[s[7]||(s[7]=t("span",null,"更多",-1)),l(r,{iconName:"auction-arrows-right",className:"more-icon"})])]),t("div",R,[n(u)?(i(),o("div",T,s[8]||(s[8]=[t("div",{class:"loading-text"},"加载中...",-1)]))):n(m).length>0?(i(),o("div",z,[(i(!0),o(v,null,h(n(m),e=>(i(),k(_,f({key:e.id},{ref_for:!0},e,{onClick:p}),null,16))),128))])):(i(),o("div",G,s[9]||(s[9]=[t("div",{class:"placeholder-text"},"暂无成交案例",-1)])))])])]))}}),cs=N(J,[["__scopeId","data-v-4c541312"]]);export{cs as default};
