import{_ as F,S as ee}from"./index-CkIekciI.js";import{b as oe,e as ae,E as i,g as te,h as ne,i as le,j as ce}from"./element-plus-DZBbDeaO.js";import{M as Q}from"./Modal-D0LrqBzM.js";import{d as T,c as R,E as L,G as A,b as e,o as _,I as h,$ as O,r as b,a as U,K as C,s as H,ax as ue,Q as se,O as q,ad as S,F as J,a1 as W,D,C as M}from"./vue-vendor-2E6AJATX.js";import{u as Y}from"./app-stores-DSLz6__G.js";import{u as E,c as me,d as ve}from"./utils-common-CvYGMv_l.js";import"./utils-http-ClVoxdfR.js";import"./crypto-vendor-SDRNRQV4.js";import{v as X,u as G,i as pe}from"./uploadUtils-BLNhP8fg.js";import{U as K}from"./UploadCard-Zoc-9CL6.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vue-router-e9iWfNxP.js";import"./http-vendor-Dq7h7Pqt.js";import"./element-icons-CT2GCbaF.js";import"./vendor-lodash-BPsl90Nm.js";const fe=T({__name:"LogoutConfirmModal",props:{modelValue:{type:Boolean},loading:{type:Boolean,default:!1}},emits:["update:modelValue","confirm","cancel"],setup(B,{emit:t}){const v=B,m=t,r=R({get:()=>v.modelValue,set:c=>m("update:modelValue",c)}),s=()=>{m("confirm")},p=()=>{m("cancel"),r.value=!1};return(c,d)=>(_(),L(Q,{modelValue:r.value,"onUpdate:modelValue":d[0]||(d[0]=x=>r.value=x),title:"退出登录",width:"400px","confirm-loading":c.loading,"confirm-button-text":"确认退出","cancel-button-text":"取消",onConfirm:s,onCancel:p},{default:A(()=>d[1]||(d[1]=[e("div",{class:"logout-confirm-content"},[e("p",{class:"logout-message"}," 确定要退出登录吗？ "),e("p",{class:"logout-note"}," 退出后需要重新登录才能使用相关功能。 ")],-1)])),_:1,__:[1]},8,["modelValue","confirm-loading"]))}}),ge=F(fe,[["__scopeId","data-v-b43d2783"]]),be={class:"delete-account-content"},_e={class:"button-section"},ye=T({__name:"DeleteAccountModal",props:{modelValue:{type:Boolean},loading:{type:Boolean,default:!1}},emits:["update:modelValue","confirm","cancel"],setup(B,{emit:t}){const v=B,m=t,r=R({get:()=>v.modelValue,set:c=>m("update:modelValue",c)}),s=()=>{m("confirm")},p=()=>{m("cancel"),r.value=!1};return(c,d)=>{const x=oe;return _(),L(Q,{modelValue:r.value,"onUpdate:modelValue":d[0]||(d[0]=$=>r.value=$),title:"注销账号",width:"658px","show-footer":!1},{default:A(()=>[e("div",be,[d[3]||(d[3]=e("p",{class:"delete-message"}," 您好！若您确认需要注销当前账号，请仔细阅读以下信息： ",-1)),d[4]||(d[4]=e("div",{class:"consequence-section"},[e("h4",{class:"section-title danger"},"一、注销后果"),e("p",{class:"consequence-text danger"}," 账号注销后，系统将永久删除您的个人资料、发布内容、互动记录等所有相关数据，且无法恢复。请慎重考虑身份需要保留的信息。 ")],-1)),d[5]||(d[5]=e("div",{class:"condition-section"},[e("h4",{class:"section-title"},"二、注销条件"),e("ul",{class:"condition-list"},[e("li",null,"账号无未完成订单，未结清款项或其他未处理"),e("li",null,"需通过绑定手机号/邮箱所有权的验证"),e("li",null,"注销后30天内若未重新激活，账号将彻底删除关闭")])],-1)),d[6]||(d[6]=e("div",{class:"special-section"},[e("h4",{class:"section-title"},"三、特别说明"),e("ul",{class:"special-list"},[e("li",null,"注销不影响第三方平台授权（如需解除除单独操作）"),e("li",null,"根据《网络安全法》等法律，我们将继续保留必要信息6个月")])],-1)),d[7]||(d[7]=e("div",{class:"question-section"},[e("h4",{class:"section-title"},"四、如有疑问"),e("p",{class:"question-text"}," 请致电客服(400-XXX-XXXX)或通过官网在线咨询，我们将协助您处理相关事宜。 ")],-1)),d[8]||(d[8]=e("div",{class:"thanks-section"},[e("p",{class:"thanks-text"},"感谢您选择我们的服务，祝您未来一切顺利！")],-1)),e("div",_e,[h(x,{class:"confirm-button",onClick:s,plain:"",loading:c.loading},{default:A(()=>d[1]||(d[1]=[O("确定")])),_:1,__:[1]},8,["loading"]),h(x,{class:"cancel-button",type:"primary",onClick:p},{default:A(()=>d[2]||(d[2]=[O("取消")])),_:1,__:[2]})])])]),_:1},8,["modelValue"])}}}),ie=F(ye,[["__scopeId","data-v-2799b9d9"]]),he={class:"personal-info"},$e={class:"user-header"},we={class:"user-avatar"},Ce={class:"avatar-bg"},ke={class:"info-list"},Ve={class:"info-item"},Pe={class:"info-content"},xe={class:"value"},Ue={class:"info-item"},Ie={class:"info-content"},qe={class:"value"},Se={class:"info-item"},Ne={class:"info-content"},Ae={class:"value"},Be={class:"info-item"},Le={class:"info-content"},Fe={class:"value"},Te={class:"info-item"},Me={class:"info-content"},Ee={class:"value"},Oe={class:"info-item"},De={class:"info-content"},ze={class:"value"},je={class:"info-item"},Re={class:"info-content"},Xe={class:"value"},Ge={key:0,class:"role-switch-content"},Ke={class:"switch-message"},Qe={class:"switch-question"},He={key:1,class:"edit-form"},Je={class:"form-item"},We=T({__name:"PersonalInfo",setup(B){const t=b({nickname:"张三",username:"张三",userId:"hg000000001",phone:"131 2345 6789",role:"竞买人",address:"河南省林州市临淇镇政府街林钢公司家属院20号1楼110室",introduction:"暂未填写个人介绍"}),v=b(!1),m=b(!1),r=b(""),s=b(""),p=b(!1),c=b(!1),d=b(!1),x=b(!1),$={nickname:"昵称",username:"用户名",phone:"联系电话",role:"用户身份",address:"地址",introduction:"个人介绍"},k=V=>{r.value=V,V!=="role"&&(s.value=t.value[V]),v.value=!0},u=async()=>{p.value=!0;try{if(await new Promise(V=>setTimeout(V,1e3)),r.value==="role"){const V=t.value.role==="竞买人"?"处置企业":"竞买人";t.value.role=V}else r.value&&s.value.trim()&&(t.value[r.value]=s.value.trim());v.value=!1}catch(V){console.error("更新失败:",V)}finally{p.value=!1}},a=()=>{v.value=!1,s.value="",r.value=""},o=()=>{m.value=!0},g=async()=>{c.value=!0;try{await new Promise(V=>setTimeout(V,1e3)),localStorage.removeItem("token"),localStorage.removeItem("userInfo"),m.value=!1}catch(V){console.error("退出登录失败:",V)}finally{c.value=!1}},y=()=>{m.value=!1},w=()=>{d.value=!0},f=async()=>{x.value=!0;try{await new Promise(V=>setTimeout(V,1e3)),d.value=!1}catch(V){console.error("注销账号失败:",V)}finally{x.value=!1}},Z=()=>{d.value=!1};return(V,n)=>{const z=ae;return _(),U("div",he,[e("div",$e,[e("div",we,[e("div",Ce,[h(ee,{iconName:"user",className:"avatar-icon"})]),n[10]||(n[10]=e("span",{class:"phone-display"},"已实名认证 131 ****6789",-1))])]),e("div",ke,[e("div",Ve,[e("div",Pe,[n[11]||(n[11]=e("div",{class:"label"},"昵称",-1)),e("div",xe,C(t.value.nickname),1)]),e("button",{class:"edit-btn",onClick:n[0]||(n[0]=N=>k("nickname"))},"编辑")]),e("div",Ue,[e("div",Ie,[n[12]||(n[12]=e("div",{class:"label"},"用户名",-1)),e("div",qe,C(t.value.username),1)]),e("button",{class:"edit-btn",onClick:n[1]||(n[1]=N=>k("username"))},"编辑")]),e("div",Se,[e("div",Ne,[n[13]||(n[13]=e("div",{class:"label"},"用户ID",-1)),e("div",Ae,C(t.value.userId),1)])]),e("div",Be,[e("div",Le,[n[14]||(n[14]=e("div",{class:"label"},"联系电话",-1)),e("div",Fe,C(t.value.phone),1)]),e("button",{class:"edit-btn",onClick:n[2]||(n[2]=N=>k("phone"))},"编辑")]),e("div",Te,[e("div",Me,[n[15]||(n[15]=e("div",{class:"label"},"用户身份",-1)),e("div",Ee,C(t.value.role),1)]),e("button",{class:"edit-btn",onClick:n[3]||(n[3]=N=>k("role"))},"切换")]),e("div",Oe,[e("div",De,[n[16]||(n[16]=e("div",{class:"label"},"地址",-1)),e("div",ze,C(t.value.address),1)]),e("button",{class:"edit-btn",onClick:n[4]||(n[4]=N=>k("address"))},"编辑")]),e("div",je,[e("div",Re,[n[17]||(n[17]=e("div",{class:"label"},"个人介绍",-1)),e("div",Xe,C(t.value.introduction),1)]),e("button",{class:"edit-btn",onClick:n[5]||(n[5]=N=>k("introduction"))},"编辑")])]),e("div",{class:"bottom-actions"},[e("button",{class:"logout-btn",onClick:o},"退出登录"),e("button",{class:"delete-account-btn",onClick:w},"注销账号")]),h(Q,{modelValue:v.value,"onUpdate:modelValue":n[7]||(n[7]=N=>v.value=N),title:r.value==="role"?"切换用户身份":`编辑${$[r.value]}`,width:"500px","confirm-loading":p.value,onConfirm:u,onCancel:a},{default:A(()=>[r.value==="role"?(_(),U("div",Ge,[e("p",Ke,[n[18]||(n[18]=O(" 您当前的身份是：")),e("strong",null,C(t.value.role),1)]),e("p",Qe,[n[19]||(n[19]=O(" 是否要切换为：")),e("strong",null,C(t.value.role==="竞买人"?"处置企业":"竞买人"),1),n[20]||(n[20]=O("？ "))]),n[21]||(n[21]=e("p",{class:"switch-note"}," 注意：切换身份后，您的权限和功能将发生变化。 ",-1))])):(_(),U("div",He,[e("div",Je,[h(z,{modelValue:s.value,"onUpdate:modelValue":n[6]||(n[6]=N=>s.value=N),placeholder:`请输入${$[r.value]}`,type:r.value==="introduction"?"textarea":"text",rows:r.value==="introduction"?4:void 0,"show-word-limit":"",clearable:""},null,8,["modelValue","placeholder","type","rows"])])]))]),_:1},8,["modelValue","title","confirm-loading"]),h(ge,{modelValue:m.value,"onUpdate:modelValue":n[8]||(n[8]=N=>m.value=N),loading:c.value,onConfirm:g,onCancel:y},null,8,["modelValue","loading"]),h(ie,{modelValue:d.value,"onUpdate:modelValue":n[9]||(n[9]=N=>d.value=N),loading:x.value,onConfirm:f,onCancel:Z},null,8,["modelValue","loading"])])}}}),Ye=F(We,[["__scopeId","data-v-9f545bcf"]]),Ze={class:"account-security"},es={class:"user-header"},ss={class:"user-avatar"},os={class:"avatar-bg"},as={class:"certification-info"},ts={class:"phone-display"},ns={class:"certification-status"},ls={class:"cert-item"},is={class:"cert-item"},rs={class:"security-list"},ds={class:"security-item"},cs={class:"security-item"},us={class:"security-content"},ms={class:"value"},vs={class:"password-form"},ps={class:"form-item"},fs={class:"form-item"},gs={class:"form-item"},bs={class:"phone-form"},_s={class:"form-item"},ys={class:"phone-display"},hs={class:"phone-number"},$s={class:"form-item"},ws={class:"verify-input"},Cs={class:"form-item"},ks=T({__name:"AccountSecurity",setup(B){const t=Y(),v=b(!1),m=b(!1),r=b(!1),s=b(!1),p=b({enterqiye:0,enteruser:0}),c=R(()=>{var P;return((P=t.userInfo)==null?void 0:P.mobile)||""}),d=R(()=>p.value.enteruser===1),x=R(()=>p.value.enterqiye===1),$=b({currentPassword:"",newPassword:"",confirmPassword:""}),k=b({currentPhone:"131 2345 6789",verifyCode:"",newPhone:""}),u=b(0),a=b(null),o=P=>{v.value=!0,$.value={currentPassword:"",newPassword:"",confirmPassword:""}},g=async()=>{var P;if((P=t.userInfo)!=null&&P.id)try{const l=await E.checkCertification({member_id:t.userInfo.id});l.code===200&&(p.value={enterqiye:l.data.enterqiye||0,enteruser:l.data.enteruser||0})}catch(l){console.error("获取认证状态失败:",l)}},y=async()=>{var P;if($.value.newPassword!==$.value.confirmPassword){i.error("两次输入的新密码不一致");return}if($.value.newPassword.length<6){i.error("密码长度至少6位");return}if(!((P=t.userInfo)!=null&&P.id)){i.error("用户信息不完整");return}r.value=!0;try{await me.changePassword({jiu_password:$.value.currentPassword,password:$.value.newPassword,member_id:t.userInfo.id}),v.value=!1,i.success("密码修改成功")}catch(l){console.error("密码修改失败:",l),i.error("密码修改失败")}finally{r.value=!1}},w=()=>{v.value=!1},f=async()=>{if(!(u.value>0))try{await new Promise(P=>setTimeout(P,500)),u.value=60,a.value=setInterval(()=>{u.value--,u.value<=0&&(clearInterval(a.value),a.value=null)},1e3),i.success("验证码已发送")}catch(P){console.error("发送验证码失败:",P),i.error("验证码发送失败")}},Z=async()=>{if(!/^1[3-9]\d{9}$/.test(k.value.newPhone.replace(/\s/g,""))){i.info("请输入正确的手机号格式");return}if(!k.value.verifyCode){i.info("请输入验证码");return}s.value=!0;try{await new Promise(l=>setTimeout(l,1e3)),m.value=!1,i.success("联系方式修改成功")}catch(l){console.error("联系方式修改失败:",l),i.error("联系方式修改失败")}finally{s.value=!1}},V=()=>{m.value=!1,a.value&&(clearInterval(a.value),a.value=null,u.value=0)},n=b(!1),z=b(!1),N=async()=>{z.value=!0;try{await new Promise(P=>setTimeout(P,2e3))}catch(P){console.error("注销账号失败:",P)}finally{z.value=!1,n.value=!1}},re=()=>{n.value=!1};return H(()=>{g()}),(P,l)=>{const j=ae,de=oe;return _(),U("div",Ze,[e("div",es,[e("div",ss,[e("div",os,[h(ee,{iconName:"user",className:"avatar-icon"})]),e("div",as,[e("div",ts,C(c.value?c.value.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):""),1),e("div",ns,[e("span",ls,"个人认证: "+C(d.value?"已认证":"未认证"),1),e("span",is,"企业认证: "+C(x.value?"已认证":"未认证"),1)])])])]),e("div",rs,[e("div",ds,[l[9]||(l[9]=ue('<div class="security-content" data-v-9a087e79><div class="label" data-v-9a087e79><span class="label-text" data-v-9a087e79>登录密码</span><span class="tips" data-v-9a087e79>(互联网账号存在被盗风险，建议您定期更改密码以保护账户安全)</span></div><div class="value" data-v-9a087e79> 已设置登录密码，建议您定期更改密码以保护账户安全 </div></div>',1)),e("button",{class:"edit-btn",onClick:l[0]||(l[0]=I=>o())},"编辑")]),e("div",cs,[e("div",us,[l[10]||(l[10]=e("div",{class:"label"},[e("span",{class:"label-text"},"联系方式"),e("span",{class:"tips"},"(建议您提供有效联系方式，随时随地获得商机)")],-1)),e("div",ms,C(c.value||"未绑定手机号"),1)])])]),h(Q,{modelValue:v.value,"onUpdate:modelValue":l[4]||(l[4]=I=>v.value=I),title:"修改登录密码",width:"658px","confirm-loading":r.value,"confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:y,onCancel:w},{default:A(()=>[e("div",vs,[e("div",ps,[l[11]||(l[11]=e("label",{class:"form-label"},"当前密码",-1)),h(j,{modelValue:$.value.currentPassword,"onUpdate:modelValue":l[1]||(l[1]=I=>$.value.currentPassword=I),type:"password",placeholder:"输入当前的密码","show-password":"",clearable:""},null,8,["modelValue"])]),e("div",fs,[l[12]||(l[12]=e("label",{class:"form-label"},"新密码",-1)),h(j,{modelValue:$.value.newPassword,"onUpdate:modelValue":l[2]||(l[2]=I=>$.value.newPassword=I),type:"password",placeholder:"请输入8位以上的新密码，需包含大小写字母、数字及特殊字符","show-password":"",clearable:""},null,8,["modelValue"])]),e("div",gs,[l[13]||(l[13]=e("label",{class:"form-label"},"确认新密码",-1)),h(j,{modelValue:$.value.confirmPassword,"onUpdate:modelValue":l[3]||(l[3]=I=>$.value.confirmPassword=I),type:"password",placeholder:"请再次确认新密码","show-password":"",clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue","confirm-loading"]),h(Q,{modelValue:m.value,"onUpdate:modelValue":l[7]||(l[7]=I=>m.value=I),title:"修改联系方式",width:"658px","confirm-loading":s.value,"confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:Z,onCancel:V},{default:A(()=>[e("div",bs,[e("div",_s,[l[15]||(l[15]=e("label",{class:"form-label"},"当前手机号",-1)),e("div",ys,[l[14]||(l[14]=e("span",{class:"country-code"},"+86",-1)),e("span",hs,C(k.value.currentPhone),1)])]),e("div",$s,[l[16]||(l[16]=e("label",{class:"form-label"},"验证码",-1)),e("div",ws,[h(j,{modelValue:k.value.verifyCode,"onUpdate:modelValue":l[5]||(l[5]=I=>k.value.verifyCode=I),placeholder:"请输入验证码",clearable:""},null,8,["modelValue"]),h(de,{type:"primary",disabled:u.value>0,onClick:f,class:"verify-btn"},{default:A(()=>[O(C(u.value>0?`${u.value}s`:"获取验证码"),1)]),_:1},8,["disabled"])])]),e("div",Cs,[l[17]||(l[17]=e("label",{class:"form-label"},"新手机号",-1)),h(j,{modelValue:k.value.newPhone,"onUpdate:modelValue":l[6]||(l[6]=I=>k.value.newPhone=I),placeholder:"请输入新手机号",clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue","confirm-loading"]),h(ie,{modelValue:n.value,"onUpdate:modelValue":l[8]||(l[8]=I=>n.value=I),loading:z.value,onConfirm:N,onCancel:re},null,8,["modelValue","loading"])])}}}),Vs=F(ks,[["__scopeId","data-v-9a087e79"]]),Ps={class:"personal-verification"},xs={class:"form-section"},Us={class:"form-item"},Is={class:"form-value"},qs={class:"form-item"},Ss={class:"form-value"},Ns={class:"form-item"},As={class:"form-value"},Bs={class:"form-item"},Ls={class:"form-value"},Fs={class:"form-item"},Ts={class:"form-value"},Ms={class:"form-item"},Es={class:"form-value"},Os={class:"form-item"},Ds={class:"form-value"},zs={class:"upload-section"},js={class:"upload-group"},Rs=T({__name:"PersonalVerification",props:{areaOptions:{},recycleOptions:{}},emits:["save","submit"],setup(B,{expose:t,emit:v}){const m=v,r=Y(),s=se({cnname:"",cardnum:"",gerenyinhang:"",gerenkahao:"",biaoqian:[],province:"",city:"",district:"",selectedArea:[],cardpicz:"",cardpicf:"",idCardFront:null,idCardBack:null,member_id:r.userInfo?r.userInfo.id:0}),p=u=>{if(Array.isArray(u)&&u.length===3){const[a,o,g]=u;s.province=a||"",s.city=o||"",s.district=g||""}},c=async u=>{try{X(u),s.idCardFront=u;const a=await G(u);s.cardpicz=a,i.success("身份证正面上传成功")}catch{i.error("身份证正面上传失败")}},d=async u=>{try{X(u),s.idCardBack=u;const a=await G(u);s.cardpicf=a,i.success("身份证反面上传成功")}catch{i.error("身份证反面上传失败")}},x=async()=>{var u;try{if(!s.cnname||!s.cardnum){i.warning("请填写完整的个人信息");return}const a={cnname:s.cnname,cardnum:s.cardnum,gerenyinhang:s.gerenyinhang,gerenkahao:s.gerenkahao,biaoqian:s.biaoqian.join(","),province:s.province,city:s.city,district:s.district,cardpicz:s.cardpicz,cardpicf:s.cardpicf,status:2,type:1,member_id:((u=r.userInfo)==null?void 0:u.id)||0};m("save",a)}catch(a){console.error("保存失败:",a),i.error("保存失败")}},$=async()=>{try{if(!s.cnname||!s.cardnum||!s.cardpicz||!s.cardpicf){i.warning("请填写完整的个人信息并上传身份证件");return}const u={cnname:s.cnname,cardnum:s.cardnum,gerenyinhang:s.gerenyinhang,gerenkahao:s.gerenkahao,biaoqian:s.biaoqian.join(","),province:s.province,city:s.city,district:s.district,cardpicz:s.cardpicz,cardpicf:s.cardpicf,status:3,type:1};m("submit",u)}catch(u){console.error("提交失败:",u),i.error("提交失败")}},k=async()=>{var u;try{const a=(u=r.userInfo)==null?void 0:u.id;if(!a)return;const o=await E.getCertificationInfo({member_id:a,type:1});if(o.code===1&&o.data){const g=o.data;s.cnname=g.cnname||"",s.cardnum=g.cardnum||"",s.gerenyinhang=g.gerenyinhang||"",s.gerenkahao=g.gerenkahao||"",s.biaoqian=g.biaoqian?g.biaoqian.split(","):[],s.province=g.province||"",s.city=g.city||"",s.district=g.district||"",s.cardpicz=g.cardpicz||"",s.cardpicf=g.cardpicf||"",s.selectedArea=[g.province.toString(),g.city.toString(),g.district.toString()]}}catch(a){console.error("加载个人认证信息失败:",a)}};return t({handleSave:x,handleSubmit:$}),H(()=>{k()}),(u,a)=>{const o=te,g=ne,y=le;return _(),U("div",Ps,[e("div",xs,[e("div",Us,[a[6]||(a[6]=e("label",{class:"form-label"},"姓名",-1)),e("div",Is,[q(e("input",{"onUpdate:modelValue":a[0]||(a[0]=w=>s.cnname=w),type:"text",class:"form-input",placeholder:"请输入姓名"},null,512),[[S,s.cnname]])])]),e("div",qs,[a[7]||(a[7]=e("label",{class:"form-label"},"身份证号",-1)),e("div",Ss,[q(e("input",{"onUpdate:modelValue":a[1]||(a[1]=w=>s.cardnum=w),type:"text",class:"form-input",placeholder:"请输入身份证号"},null,512),[[S,s.cardnum]])])]),e("div",Ns,[a[8]||(a[8]=e("label",{class:"form-label"},"开户行",-1)),e("div",As,[q(e("input",{"onUpdate:modelValue":a[2]||(a[2]=w=>s.gerenyinhang=w),type:"text",class:"form-input",placeholder:"请输入开户行"},null,512),[[S,s.gerenyinhang]])])]),e("div",Bs,[a[9]||(a[9]=e("label",{class:"form-label"},"银行账号",-1)),e("div",Ls,[q(e("input",{"onUpdate:modelValue":a[3]||(a[3]=w=>s.gerenkahao=w),type:"text",class:"form-input",placeholder:"请输入银行账号"},null,512),[[S,s.gerenkahao]])])]),e("div",Fs,[a[10]||(a[10]=e("label",{class:"form-label"},"回收种类",-1)),e("div",Ts,[h(g,{modelValue:s.biaoqian,"onUpdate:modelValue":a[4]||(a[4]=w=>s.biaoqian=w),class:"checkbox-group"},{default:A(()=>[(_(!0),U(J,null,W(u.recycleOptions,w=>(_(),L(o,{key:w.value,value:w.value,class:"checkbox-item"},{default:A(()=>[O(C(w.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])]),e("div",Ms,[a[11]||(a[11]=e("label",{class:"form-label"},"所属地区",-1)),e("div",Es,[h(y,{modelValue:s.selectedArea,"onUpdate:modelValue":a[5]||(a[5]=w=>s.selectedArea=w),options:u.areaOptions,props:{expandTrigger:"hover"},placeholder:"请选择省市区",class:"area-cascader",onChange:p},null,8,["modelValue","options"])])]),e("div",Os,[a[13]||(a[13]=e("label",{class:"form-label"},"上传证件",-1)),e("div",Ds,[e("div",zs,[a[12]||(a[12]=e("p",{class:"upload-description"}," 请上传身份证件，要求为彩色，最多可上传2个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。 ",-1)),e("div",js,[h(K,{uploadText:"身份证正面",backgroundImage:"@/assets/icons/upload/idcard-front.svg",imageUrl:`${s.cardpicz}`,onUpload:c},null,8,["imageUrl"]),h(K,{uploadText:"身份证反面",backgroundImage:"@/assets/icons/upload/idcard-reverse.svg",imageUrl:s.cardpicf,onUpload:d},null,8,["imageUrl"])])])])])])])}}}),Xs=F(Rs,[["__scopeId","data-v-32f37f9a"]]),Gs={class:"enterprise-verification"},Ks={class:"form-section"},Qs={class:"form-item"},Hs={class:"form-value"},Js={class:"form-item"},Ws={class:"form-value"},Ys={class:"form-item"},Zs={class:"form-value"},eo={class:"form-item"},so={class:"form-value"},oo={class:"form-item"},ao={class:"form-value"},to={class:"form-item"},no={class:"form-value"},lo={class:"form-item"},io={class:"form-value"},ro={class:"form-item"},co={class:"form-value"},uo={class:"form-item"},mo={class:"form-value"},vo={class:"form-item"},po={class:"form-value"},fo={class:"form-item"},go={class:"form-value"},bo={class:"form-item"},_o={class:"form-value"},yo={class:"upload-section"},ho={class:"form-item"},$o={class:"form-value"},wo={class:"form-item"},Co={class:"form-value"},ko={class:"form-item"},Vo={class:"form-value"},Po={class:"upload-section"},xo={class:"upload-group"},Uo=T({__name:"EnterpriseVerification",props:{areaOptions:{},recycleOptions:{}},emits:["save","submit"],setup(B,{expose:t,emit:v}){const m=v,r=Y(),s=se({companyName:"",creditCode:"",contactPerson:"",contactPhone:"",address:"",kpdianhua:"",kaihuhang:"",gonghu:"",hanghao:"",biaoqian:[],selectedArea:[],province:"",city:"",district:"",legalPersonName:"",legalPersonIdCard:"",businessLicense:null,businessLicenseUrl:"",legalPersonFront:null,legalPersonFrontUrl:"",legalPersonBack:null,legalPersonBackUrl:"",member_id:r.userInfo?r.userInfo.id:0}),p=async a=>{try{X(a),s.businessLicense=a;const o=await G(a);s.businessLicenseUrl=o,i.success("营业执照上传成功")}catch{i.error("营业执照上传失败")}},c=async a=>{try{X(a),s.legalPersonFront=a;const o=await G(a);s.legalPersonFrontUrl=o,i.success("法人身份证正面上传成功")}catch{i.error("法人身份证正面上传失败")}},d=async a=>{try{X(a),s.legalPersonBack=a;const o=await G(a);s.legalPersonBackUrl=o,i.success("法人身份证反面上传成功")}catch{i.error("法人身份证反面上传失败")}},x=a=>{a&&a.length===3?(s.province=a[0],s.city=a[1],s.district=a[2]):(s.province="",s.city="",s.district="")},$=async()=>{var a;try{if(!s.companyName||!s.creditCode){i.warning("请填写完整的企业信息");return}const o={qiyemingcheng:s.companyName,xinyongdaima:s.creditCode,lianxiren:s.contactPerson,lianxidianhua:s.contactPhone,address:s.address,kpdianhua:s.kpdianhua,kaihuhang:s.kaihuhang,gonghu:s.gonghu,hanghao:s.hanghao,biaoqian:s.biaoqian.join(","),province:s.province,city:s.city,district:s.district,fr_name:s.legalPersonName,fr_cardnum:s.legalPersonIdCard,qiyepic:s.businessLicenseUrl,fr_cardpicz:s.legalPersonFrontUrl,fr_cardpicf:s.legalPersonBackUrl,status:2,type:2,member_id:((a=r.userInfo)==null?void 0:a.id)||0};m("save",o)}catch(o){console.error("保存失败:",o),i.error("保存失败")}},k=async()=>{var a;try{if(!s.companyName||!s.creditCode||!s.businessLicenseUrl||!s.legalPersonFrontUrl||!s.legalPersonBackUrl){i.warning("请填写完整的企业信息并上传相关证件");return}const o={qiyemingcheng:s.companyName,xinyongdaima:s.creditCode,lianxiren:s.contactPerson,lianxidianhua:s.contactPhone,address:s.address,kpdianhua:s.kpdianhua,kaihuhang:s.kaihuhang,gonghu:s.gonghu,hanghao:s.hanghao,biaoqian:s.biaoqian.join(","),province:s.province,city:s.city,district:s.district,fr_name:s.legalPersonName,fr_cardnum:s.legalPersonIdCard,qiyepic:s.businessLicenseUrl,fr_cardpicz:s.legalPersonFrontUrl,fr_cardpicf:s.legalPersonBackUrl,status:3,type:2,member_id:((a=r.userInfo)==null?void 0:a.id)||0};m("submit",o)}catch(o){console.error("提交失败:",o),i.error("提交失败")}},u=async()=>{var a;try{const o=(a=r.userInfo)==null?void 0:a.id;if(!o)return;const g=await E.getCertificationInfo({member_id:o,type:2});if(g.code===1&&g.data){const y=g.data;s.companyName=y.qiyemingcheng||"",s.creditCode=y.xinyongdaima||"",s.contactPerson=y.lianxiren||"",s.contactPhone=y.lianxidianhua||"",s.address=y.address||"",s.kpdianhua=y.kpdianhua||"",s.kaihuhang=y.kaihuhang||"",s.gonghu=y.gonghu||"",s.hanghao=y.hanghao||"",y.biaoqian&&(s.biaoqian=y.biaoqian.split(",")),s.legalPersonName=y.fr_name||"",s.legalPersonIdCard=y.fr_cardnum||"",s.businessLicenseUrl=y.qiyepic||"",s.legalPersonFrontUrl=y.fr_cardpicz||"",s.legalPersonBackUrl=y.fr_cardpicf||"",s.selectedArea=[y.province.toString(),y.city.toString(),y.district.toString()]}}catch(o){console.error("加载企业认证信息失败:",o)}};return t({handleSave:$,handleSubmit:k}),H(()=>{u()}),(a,o)=>{const g=te,y=ne,w=le;return _(),U("div",Gs,[e("div",Ks,[e("div",Qs,[o[13]||(o[13]=e("label",{class:"form-label"},"企业名称",-1)),e("div",Hs,[q(e("input",{"onUpdate:modelValue":o[0]||(o[0]=f=>s.companyName=f),type:"text",class:"form-input",placeholder:"请输入企业名称"},null,512),[[S,s.companyName]])])]),e("div",Js,[o[14]||(o[14]=e("label",{class:"form-label"},"统一社会信用代码",-1)),e("div",Ws,[q(e("input",{"onUpdate:modelValue":o[1]||(o[1]=f=>s.creditCode=f),type:"text",class:"form-input",placeholder:"请输入统一社会信用代码"},null,512),[[S,s.creditCode]])])]),e("div",Ys,[o[15]||(o[15]=e("label",{class:"form-label"},"业务联系人",-1)),e("div",Zs,[q(e("input",{"onUpdate:modelValue":o[2]||(o[2]=f=>s.contactPerson=f),type:"text",class:"form-input",placeholder:"请输入业务联系人"},null,512),[[S,s.contactPerson]])])]),e("div",eo,[o[16]||(o[16]=e("label",{class:"form-label"},"联系电话",-1)),e("div",so,[q(e("input",{"onUpdate:modelValue":o[3]||(o[3]=f=>s.contactPhone=f),type:"text",class:"form-input",placeholder:"请输入联系电话"},null,512),[[S,s.contactPhone]])])]),e("div",oo,[o[17]||(o[17]=e("label",{class:"form-label"},"公司地址",-1)),e("div",ao,[q(e("input",{"onUpdate:modelValue":o[4]||(o[4]=f=>s.address=f),type:"text",class:"form-input",placeholder:"请输入公司地址"},null,512),[[S,s.address]])])]),e("div",to,[o[18]||(o[18]=e("label",{class:"form-label"},"开票电话",-1)),e("div",no,[q(e("input",{"onUpdate:modelValue":o[5]||(o[5]=f=>s.kpdianhua=f),type:"text",class:"form-input",placeholder:"请输入开票电话"},null,512),[[S,s.kpdianhua]])])]),e("div",lo,[o[19]||(o[19]=e("label",{class:"form-label"},"开户行",-1)),e("div",io,[q(e("input",{"onUpdate:modelValue":o[6]||(o[6]=f=>s.kaihuhang=f),type:"text",class:"form-input",placeholder:"请输入开户行"},null,512),[[S,s.kaihuhang]])])]),e("div",ro,[o[20]||(o[20]=e("label",{class:"form-label"},"公司账户",-1)),e("div",co,[q(e("input",{"onUpdate:modelValue":o[7]||(o[7]=f=>s.gonghu=f),type:"text",class:"form-input",placeholder:"请输入公司账户"},null,512),[[S,s.gonghu]])])]),e("div",uo,[o[21]||(o[21]=e("label",{class:"form-label"},"行号",-1)),e("div",mo,[q(e("input",{"onUpdate:modelValue":o[8]||(o[8]=f=>s.hanghao=f),type:"text",class:"form-input",placeholder:"请输入行号"},null,512),[[S,s.hanghao]])])]),e("div",vo,[o[22]||(o[22]=e("label",{class:"form-label"},"回收种类",-1)),e("div",po,[h(y,{modelValue:s.biaoqian,"onUpdate:modelValue":o[9]||(o[9]=f=>s.biaoqian=f),class:"checkbox-group"},{default:A(()=>[(_(!0),U(J,null,W(a.recycleOptions,f=>(_(),L(g,{key:f.value,value:f.value,class:"checkbox-item"},{default:A(()=>[O(C(f.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])]),e("div",fo,[o[23]||(o[23]=e("label",{class:"form-label"},"所属地区",-1)),e("div",go,[h(w,{modelValue:s.selectedArea,"onUpdate:modelValue":o[10]||(o[10]=f=>s.selectedArea=f),options:a.areaOptions,props:{expandTrigger:"hover"},placeholder:"请选择省市区",class:"area-cascader",onChange:x},null,8,["modelValue","options"])])]),e("div",bo,[o[25]||(o[25]=e("label",{class:"form-label"},"营业执照",-1)),e("div",_o,[e("div",yo,[o[24]||(o[24]=e("p",{class:"upload-description"}," 请上传营业执照副本，要求为彩色，最多可上传1个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。 ",-1)),h(K,{uploadText:"营业执照",backgroundImage:"@/assets/icons/upload/business-license.svg",imageUrl:s.businessLicenseUrl,onUpload:p},null,8,["imageUrl"])])])]),e("div",ho,[o[26]||(o[26]=e("label",{class:"form-label"},"法人姓名",-1)),e("div",$o,[q(e("input",{"onUpdate:modelValue":o[11]||(o[11]=f=>s.legalPersonName=f),type:"text",class:"form-input",placeholder:"请输入法人姓名"},null,512),[[S,s.legalPersonName]])])]),e("div",wo,[o[27]||(o[27]=e("label",{class:"form-label"},"法人身份证号",-1)),e("div",Co,[q(e("input",{"onUpdate:modelValue":o[12]||(o[12]=f=>s.legalPersonIdCard=f),type:"text",class:"form-input",placeholder:"请输入法人身份证号"},null,512),[[S,s.legalPersonIdCard]])])]),e("div",ko,[o[29]||(o[29]=e("label",{class:"form-label"},"上传法人证件",-1)),e("div",Vo,[e("div",Po,[o[28]||(o[28]=e("p",{class:"upload-description"}," 请上传法人证件，要求为彩色，最多可上传2个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。 ",-1)),e("div",xo,[h(K,{uploadText:"身份证正面",backgroundImage:"@/assets/icons/upload/idcard-front.svg",imageUrl:s.legalPersonFrontUrl,onUpload:c},null,8,["imageUrl"]),h(K,{uploadText:"身份证反面",backgroundImage:"@/assets/icons/upload/idcard-reverse.svg",imageUrl:s.legalPersonBackUrl,onUpload:d},null,8,["imageUrl"])])])])])])])}}}),Io=F(Uo,[["__scopeId","data-v-311aa2b3"]]),qo={class:"verification-container"},So={class:"verification-nav"},No={class:"verification-content"},Ao=T({__name:"Verification",setup(B){const t=b(2),v=b([]),m=b([{value:"1",label:"物资设备"},{value:"2",label:"机动车"},{value:"3",label:"房产"},{value:"4",label:"土地"},{value:"5",label:"其他"}]),r=b(),s=b(),p=a=>{t.value=a},c=async a=>{try{const o=await E.uploadCertification(a);o.code===1?i.success("保存成功"):i.error(o.msg||"保存失败")}catch(o){console.error("保存失败:",o),i.error("保存失败")}},d=async a=>{try{const o=await E.uploadCertification(a);o.code===200?i.success("提交审核成功"):i.error(o.msg||"提交失败")}catch(o){console.error("提交失败:",o),i.error("提交失败")}},x=async a=>{try{const o=await E.uploadCertification(a);o.code===1?i.success("保存成功"):i.error(o.msg||"保存失败")}catch(o){console.error("保存失败:",o),i.error("保存失败")}},$=async a=>{try{const o=await E.uploadCertification(a);o.code===200?i.success("提交审核成功"):i.error(o.msg||"提交失败")}catch(o){console.error("提交失败:",o),i.error("提交失败")}},k=()=>{var a,o;t.value===1?(a=r.value)==null||a.handleSave():(o=s.value)==null||o.handleSave()},u=()=>{var a,o;t.value===1?(a=r.value)==null||a.handleSubmit():(o=s.value)==null||o.handleSubmit()};return H(()=>{v.value=pe()}),(a,o)=>(_(),U("div",qo,[e("div",So,[e("div",{class:D(["nav-tab enterprise-nav-tab",{active:t.value===2}]),onClick:o[0]||(o[0]=g=>p(2))}," 企业认证 ",2),e("div",{class:D(["nav-tab personal-nav-tab",{active:t.value===1}]),onClick:o[1]||(o[1]=g=>p(1))}," 个人认证 ",2)]),e("div",No,[t.value===2?(_(),L(Io,{key:0,ref_key:"enterpriseVerificationRef",ref:s,areaOptions:v.value,recycleOptions:m.value,onSave:x,onSubmit:$},null,8,["areaOptions","recycleOptions"])):M("",!0),t.value===1?(_(),L(Xs,{key:1,ref_key:"personalVerificationRef",ref:r,areaOptions:v.value,recycleOptions:m.value,onSave:c,onSubmit:d},null,8,["areaOptions","recycleOptions"])):M("",!0)]),e("div",{class:"action-buttons"},[e("button",{class:"save-btn",onClick:k},"保存"),e("button",{class:"submit-btn",onClick:u},"提交审核")])]))}}),Bo=F(Ao,[["__scopeId","data-v-852f5a87"]]),Lo={class:"invoice-container"},Fo={class:"invoice-nav"},To={class:"invoice-content"},Mo={key:0,class:"form-section"},Eo={class:"form-item"},Oo={class:"form-value"},Do={key:1,class:"form-section"},zo=T({__name:"Invoice",setup(B){const t=b("enterprise"),v=se({companyName:""}),m=p=>{t.value=p},r=()=>{i.success("保存成功")},s=()=>{if(t.value==="enterprise"){if(!v.companyName){i.warning("请填写企业名称");return}}else{i.info("个人认证功能开发中...");return}i.success("提交审核成功")};return(p,c)=>(_(),U("div",Lo,[e("div",Fo,[e("div",{class:D(["nav-tab",{active:t.value==="enterprise"}]),onClick:c[0]||(c[0]=d=>m("enterprise"))}," 企业认证 ",2),e("div",{class:D(["nav-tab",{active:t.value==="personal"}]),onClick:c[1]||(c[1]=d=>m("personal"))}," 个人认证 ",2)]),e("div",To,[t.value==="enterprise"?(_(),U("div",Mo,[e("div",Eo,[c[3]||(c[3]=e("label",{class:"form-label"},"企业名称",-1)),e("div",Oo,[q(e("input",{"onUpdate:modelValue":c[2]||(c[2]=d=>v.companyName=d),type:"text",class:"form-input",placeholder:"请输入企业名称"},null,512),[[S,v.companyName]])])])])):M("",!0),t.value==="personal"?(_(),U("div",Do,c[4]||(c[4]=[e("div",{class:"empty-state"},[e("p",{class:"empty-text"},"个人认证功能开发中...")],-1)]))):M("",!0)]),e("div",{class:"action-buttons"},[e("button",{class:"save-btn",onClick:r},"保存"),e("button",{class:"submit-btn",onClick:s},"提交审核")])]))}}),jo=F(zo,[["__scopeId","data-v-cd950ef4"]]),Ro={class:"followed-companies"},Xo={class:"header-section"},Go={class:"follow-count"},Ko={class:"count"},Qo={class:"companies-list"},Ho={class:"company-avatar"},Jo=["src","alt"],Wo={class:"company-info"},Yo={class:"company-name"},Zo={class:"company-description"},ea=["onClick"],sa=T({__name:"FollowedCompanies",setup(B){const t=Y();b("");const v=b([]);H(()=>{m()});const m=async()=>{var p;const s=await E.getMyFavoriteCompanies({member_id:(p=t.userInfo)==null?void 0:p.id,page:1,type:1});s.code===1&&(v.value=s.data.data)},r=async s=>{var c;await ce.confirm("确定取消关注吗？","取消关注",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&(await ve.favoriteCompany({member_id:(c=t.userInfo)==null?void 0:c.id,qiye_id:s,isquxiao:0,type:1})).code===1&&(i.success("取消关注成功"),m())};return(s,p)=>(_(),U("div",Ro,[e("div",Xo,[e("div",Go,[e("span",Ko,C(v.value.length),1),p[0]||(p[0]=e("span",{class:"text"},"关注企业",-1))])]),e("div",Qo,[(_(!0),U(J,null,W(v.value,(c,d)=>(_(),U("div",{class:D(["company-item",{"no-border":d===v.value.length-1}]),key:c.id},[e("div",Ho,[e("img",{src:`https://huigupaimai.oss-cn-beijing.aliyuncs.com/${c.qiye.qiyelogo}`,alt:c.qiye.qiyemingcheng,class:"avatar-img"},null,8,Jo)]),e("div",Wo,[e("div",Yo,C(c.qiye.qiyemingcheng),1),e("div",Zo,C(c.qiye.qiyephone),1)]),e("button",{class:"followed-btn",onClick:x=>r(c.qiye_id)}," 取消关注 ",8,ea)],2))),128))])]))}}),oa=F(sa,[["__scopeId","data-v-42e2fac5"]]),aa={class:"profile-container"},ta={class:"profile-sidebar"},na={class:"sidebar-nav"},la=["onClick"],ia={class:"nav-text"},ra={class:"profile-content"},da=T({__name:"index",setup(B){const t=b("security"),v=[{key:"security",label:"账号安全",icon:"login-data-security"},{key:"verification",label:"实名认证",icon:"login-data-real-name"},{key:"follow",label:"关注企业",icon:"login-data-attention"}],m=r=>{t.value=r};return(r,s)=>(_(),U("div",aa,[e("div",ta,[s[0]||(s[0]=e("div",{class:"sidebar-title"},"我的资料",-1)),s[1]||(s[1]=e("div",{class:"sidebar-divider"},null,-1)),e("div",na,[(_(),U(J,null,W(v,p=>e("div",{key:p.key,class:D(["nav-item",{active:t.value===p.key}]),onClick:c=>m(p.key)},[h(ee,{iconName:p.icon,class:"nav-icon"},null,8,["iconName"]),e("span",ia,C(p.label),1)],10,la)),64))])]),e("div",ra,[t.value==="personal"?(_(),L(Ye,{key:0})):M("",!0),t.value==="security"?(_(),L(Vs,{key:1})):M("",!0),t.value==="verification"?(_(),L(Bo,{key:2})):M("",!0),t.value==="invoice"?(_(),L(jo,{key:3})):M("",!0),t.value==="follow"?(_(),L(oa,{key:4})):M("",!0)])]))}}),Ia=F(da,[["__scopeId","data-v-29f9f33b"]]);export{Ia as default};
