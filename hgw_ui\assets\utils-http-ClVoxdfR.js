import{a as i}from"./http-vendor-Dq7h7Pqt.js";import{E as r}from"./element-plus-DZBbDeaO.js";const n=i.create({baseURL:"http://39.101.72.34:80",timeout:1e4,withCredentials:!1});function c(){return navigator.appName==="Microsoft Internet Explorer"&&parseInt(navigator.appVersion.split(";")[1].replace(/[ ]/g,"").replace("MSIE",""))<=9}n.interceptors.response.use(e=>(c()&&e.status===200&&e.request&&e.request.responseType==="json"&&e.request.responseText&&(e.data=JSON.parse(e.request.responseText)),e),e=>{if(e.response)switch(console.error("HTTP Error:",e),e.response.status){case 401:r.error("未授权，请重新登录");break;case 403:r.error("拒绝访问");break;case 404:r.error("请求的资源不存在");break;case 500:r.error("服务器内部错误");break;default:r.error("网络错误，请稍后重试")}else e.request?r.error("网络连接超时，请检查网络"):r.error("请求配置错误");return Promise.reject(e)});n.interceptors.request.use(e=>e,e=>Promise.reject(e));const p=i.create({baseURL:"http://39.101.72.34:18080/jeecgboot",timeout:1e4,withCredentials:!1});function l(){return navigator.appName==="Microsoft Internet Explorer"&&parseInt(navigator.appVersion.split(";")[1].replace(/[ ]/g,"").replace("MSIE",""))<=9}p.interceptors.response.use(e=>(l()&&e.status===200&&e.request&&e.request.responseType==="json"&&e.request.responseText&&(e.data=JSON.parse(e.request.responseText)),e),e=>{if(e.response)switch(console.error("HTTP Error:",e),e.response.status){case 401:r.error("未授权，请重新登录");break;case 403:r.error("拒绝访问");break;case 404:r.error("请求的资源不存在");break;case 500:r.error("服务器内部错误");break;default:r.error("网络错误，请稍后重试")}else e.request?r.error("网络连接超时，请检查网络"):r.error("请求配置错误");return Promise.reject(e)});p.interceptors.request.use(e=>{var o,u;const s=localStorage.getItem("freedomToken")||sessionStorage.getItem("freedomToken");s&&(e.headers["x-access-token"]=s);const a=new Date().getTime();if(e.method==="get"){const t=(o=e.url)!=null&&o.includes("?")?"&":"?";e.url=`${e.url}${t}_t=${a}`}else{const t=(u=e.url)!=null&&u.includes("?")?"&":"?";e.url=`${e.url}${t}_t=${a}`}return e},e=>Promise.reject(e));export{p as a,n as h};
