import{u as s}from"./vue-router-C0lzQS1p.js";import{u as i,S as a,A as e,_ as t}from"./index-CCmno-0X.js";import{C as n}from"./Carousel-CdeEQzqk.js";import{d as o,r as l,s as c,a as r,b as d,I as m,u as p,F as u,a1 as v,E as g,q as h,o as j}from"./vue-vendor-D6tHD5lA.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./utils-common-PdkFOSu3.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./element-plus-BiAL0NdQ.js";import"./element-icons-Cfq32zG_.js";import"./vendor-lodash-D0OfQ6x6.js";import"./crypto-vendor-CkZKNqwc.js";import"./app-stores-CLUCXxRF.js";const y={class:"bidding"},b={class:"carousel-container"},k={class:"section"},f={class:"section-title"},I={class:"title-container"},N={class:"content-container"},_={key:0,class:"loading"},x={key:1,class:"auction-cards"},C={key:2,class:"placeholder"},q={class:"section"},w={class:"section-title"},A={class:"title-container"},F={class:"content-container"},B={key:0,class:"loading"},D={key:1,class:"auction-cards"},E={key:2,class:"placeholder"},H=t(o({__name:"BiddingHome",setup(t){const o=s(),{auctionItems:H,accomplishItems:S,loading:T,initAuctions:z}=i(),G=l([{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/banner-bidding_1754963452117.jpg",title:""},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",title:"推广banner图展示位"},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",title:"推广banner图展示位"}]);c(async()=>{await z()});const J=s=>{o.push({name:"auctionDetail",query:{id:s.productId,pmhId:s.pmhId,crumbsTitle:"竞价交易"}})},K=(s,i)=>{o.push({name:"biddingInfo",query:{name:s,type:i}})};return(s,i)=>(j(),r("div",y,[d("div",b,[m(n,{items:G.value,autoplay:!0,interval:5e3},null,8,["items"])]),d("div",k,[d("div",f,[d("div",I,[m(a,{iconName:"bidding-recommend",className:"title-icon"}),i[2]||(i[2]=d("span",null,"推荐标的",-1))]),d("div",{class:"more-container",onClick:i[0]||(i[0]=s=>K("",""))},[i[3]||(i[3]=d("span",null,"更多",-1)),m(a,{iconName:"auction-arrows-right",className:"more-icon"})])]),d("div",N,[p(T)?(j(),r("div",_,i[4]||(i[4]=[d("div",{class:"loading-text"},"加载中...",-1)]))):p(H).length>0?(j(),r("div",x,[(j(!0),r(u,null,v(p(H),s=>(j(),g(e,h({key:s.id},{ref_for:!0},s,{onClick:J}),null,16))),128))])):(j(),r("div",C,i[5]||(i[5]=[d("div",{class:"placeholder-text"},"暂无推荐标的",-1)])))])]),d("div",q,[d("div",w,[d("div",A,[m(a,{iconName:"bidding-case",className:"title-icon"}),i[6]||(i[6]=d("p",null,"成交案例",-1))]),d("div",{class:"more-container",onClick:i[1]||(i[1]=s=>K("status","completed"))},[i[7]||(i[7]=d("span",null,"更多",-1)),m(a,{iconName:"auction-arrows-right",className:"more-icon"})])]),d("div",F,[p(T)?(j(),r("div",B,i[8]||(i[8]=[d("div",{class:"loading-text"},"加载中...",-1)]))):p(S).length>0?(j(),r("div",D,[(j(!0),r(u,null,v(p(S),s=>(j(),g(e,h({key:s.id},{ref_for:!0},s,{onClick:J}),null,16))),128))])):(j(),r("div",E,i[9]||(i[9]=[d("div",{class:"placeholder-text"},"暂无成交案例",-1)])))])])]))}}),[["__scopeId","data-v-4c541312"]]);export{H as default};
