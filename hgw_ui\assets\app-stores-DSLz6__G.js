import{d as l}from"./pinia-OEfr7ZxB.js";import{r as g,c}from"./vue-vendor-2E6AJATX.js";const S=l("user",()=>{const e=g(null),n=c(()=>e.value!==null),a=t=>{e.value=t,localStorage.setItem("apptoken",t.apptoken),localStorage.setItem("userInfo",JSON.stringify(t)),localStorage.setItem("isLoggedIn","true")},r=()=>{e.value=null,localStorage.removeItem("userInfo"),localStorage.removeItem("isLoggedIn"),localStorage.removeItem("apptoken")};return{userInfo:e,isLoggedIn:n,login:a,logout:r,initUserState:()=>{const t=localStorage.getItem("userInfo");if(localStorage.getItem("isLoggedIn")==="true"&&t)try{e.value=JSON.parse(t)}catch(o){console.error("解析用户信息失败:",o),r()}}}}),f=l("freedomUser",()=>{const e=g(null),n=c(()=>e.value!==null&&e.value.token!==""),a=o=>{e.value=o,localStorage.setItem("freedomToken",o.token),localStorage.setItem("freedomUserInfo",JSON.stringify(o)),localStorage.setItem("isFreedomLoggedIn","true")},r=()=>{e.value=null,localStorage.removeItem("freedomUserInfo"),localStorage.removeItem("isFreedomLoggedIn"),localStorage.removeItem("freedomToken")},s=()=>{const o=localStorage.getItem("freedomUserInfo");if(localStorage.getItem("isFreedomLoggedIn")==="true"&&o)try{e.value=JSON.parse(o)}catch(I){console.error("解析自由交易用户信息失败:",I),r()}},t=()=>localStorage.getItem("freedomToken")||"";return{freedomUserInfo:e,isFreedomLoggedIn:n,freedomLogin:a,freedomLogout:r,initFreedomUserState:s,getFreedomToken:t,checkFreedomLogin:()=>{const o=t();return localStorage.getItem("isFreedomLoggedIn")==="true"&&o!==""&&o!=="undefined"}}});export{f as a,S as u};
