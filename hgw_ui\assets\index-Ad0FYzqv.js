import{S as t,A as r,a as m,C as i}from"./index-CkIekciI.js";import{M as n}from"./Modal-D0LrqBzM.js";import{C as p}from"./Carousel-CaEHbRFW.js";import{P as a,C as e}from"./PropertyCard-zkRWinEN.js";import{S as c}from"./ServiceSupport-DDmYIEj8.js";import{U as s}from"./UploadCard-Zoc-9CL6.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./vue-vendor-2E6AJATX.js";import"./pinia-OEfr7ZxB.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vue-router-e9iWfNxP.js";import"./utils-common-CvYGMv_l.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./element-plus-DZBbDeaO.js";import"./element-icons-CT2GCbaF.js";import"./vendor-lodash-BPsl90Nm.js";import"./crypto-vendor-SDRNRQV4.js";import"./app-stores-DSLz6__G.js";const q={install(o){o.component("SvgIcon",t),o.component("Modal",n),o.component("Carousel",p),o.component("PropertyCard",a),o.component("ServiceSupport",c),o.component("AuctionCard",r),o.component("AuctionListItem",m),o.component("CompanyTag",e),o.component("UploadCard",s),o.component("CustomScrollbar",i)}};export{q as default};
