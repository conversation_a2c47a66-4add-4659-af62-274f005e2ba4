import{S as a}from"./ServiceSupport-DDmYIEj8.js";import{d as p,a as c,I as m,o as d}from"./vue-vendor-2E6AJATX.js";import{_ as l}from"./index-CkIekciI.js";import"./element-plus-DZBbDeaO.js";import"./element-icons-CT2GCbaF.js";import"./vueuse-vendor-DVBEw4tM.js";import"./vendor-others-D4FIkGzs.js";import"./vendor-lodash-BPsl90Nm.js";import"./app-assets-WDvSAEaN.js";/* empty css                         */import"./pinia-OEfr7ZxB.js";import"./vue-router-e9iWfNxP.js";import"./utils-common-CvYGMv_l.js";import"./utils-http-ClVoxdfR.js";import"./http-vendor-Dq7h7Pqt.js";import"./crypto-vendor-SDRNRQV4.js";import"./app-stores-DSLz6__G.js";const u={class:"page"},_=p({__name:"BiddingSupport",setup(g){const o=[{icon:"support-question",text:"常见问题"},{icon:"support-bidding-register",text:"注册流程"},{icon:"support-bidding-auction",text:"竞拍规则"},{icon:"support-bidding-industry",text:"行业资讯"}],i=[[{title:"如何注册账户？",answer:'您可以点击页面右上角的"注册"按钮，填写相关信息完成注册。'},{title:"忘记密码怎么办？",answer:'可以通过"忘记密码"功能重置密码，系统会发送验证码到您的手机。'}],[{title:"如何完成实名认证？",answer:"进入个人中心，上传身份证照片并填写真实信息即可。"}],[{title:"支持哪些支付方式？",answer:"支持支付宝、微信支付、银行卡等多种支付方式。"}],[{title:"如何参与竞拍？",answer:"完成实名认证后，缴纳保证金即可参与竞拍。"}]],r=t=>{alert(`搜索: ${t}`)},s=async t=>(console.log("获取问题列表，导航索引:",t),await new Promise(e=>setTimeout(e,300)),i[t]||[]),n=(t,e)=>{console.log("反馈:",{questionIndex:t,isHelpful:e}),alert(e?"感谢您的反馈！":"我们会继续改进！")};return(t,e)=>(d(),c("div",u,[m(a,{title:"嗨！有什么需要帮忙的吗？",placeholder:"搜索答案","navigation-items":o,"on-search":r,"on-get-questions":s,"on-helpful-feedback":n})]))}}),P=l(_,[["__scopeId","data-v-0dade945"]]);export{P as default};
