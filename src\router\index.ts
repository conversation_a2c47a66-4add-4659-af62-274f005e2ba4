import { createRouter, createWebHashHistory } from "vue-router";
import HomeView from "../views/HomeView.vue";
import Layout from "../layout/index.vue";

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      component: Layout,
      children: [
        {
          path: "",
          name: "home",
          meta: {
            title: "推荐专区",
          },
          component: HomeView,
        },
        // 登录页面路由
        {
          path: "/login",
          name: "login",
          meta: {
            title: "登录",
          },
          component: () => import("../views/login/index.vue"),
        },
        // 自由交易登录页面路由
        {
          path: "/freedom-login",
          name: "freedomLogin",
          meta: {
            title: "自由交易登录",
          },
          component: () => import("../views/FreedomLogin/index.vue"),
        },
        // 关于我们页面路由
        {
          path: "/about",
          name: "about",
          meta: {
            title: "关于我们",
          },
          component: () => import("../views/AboutUs.vue"),
        },
        // 我的资料页面路由
        {
          path: "/profile",
          name: "profile",
          meta: {
            title: "我的资料",
          },
          component: () => import("../views/Profile/index.vue"),
        },
        // 竞拍管理页面路由
        {
          path: "/biddingManage",
          name: "biddingManage",
          meta: {
            title: "竞拍管理",
          },
          component: () => import("../views/BiddingManage/index.vue"),
        },
        {
          path: "/bidding",
          name: "bidding",
          meta: {
            title: "竞拍",
          },
          component: () => import("../views/Bidding/index.vue"),
          children: [
            {
              path: "biddingHome",
              name: "biddingHome",
              meta: {
                title: "竞价交易",
              },
              component: () =>
                import("../views/Bidding/childrenView/BiddingHome.vue"),
            },
            {
              path: "biddingInfo",
              name: "biddingInfo",
              meta: {
                title: "标的信息",
              },
              component: () =>
                import("../views/Bidding/childrenView/BiddingInfo.vue"),
            },
            {
              path: "biddingEnterprise",
              name: "biddingEnterprise",
              meta: {
                title: "企业合作",
              },
              component: () =>
                import("../views/Bidding/childrenView/BiddingEnterprise.vue"),
            },
            {
              path: "biddingSupport",
              name: "biddingSupport",
              meta: {
                title: "服务支持",
              },
              component: () =>
                import("../views/Bidding/childrenView/BiddingSupport.vue"),
            },
          ],
        },
        {
          path: "/freedom",
          name: "freedom",
          meta: {
            title: "自由交易",
          },
          component: () => import("../views/Freedom/index.vue"),
          children: [
            {
              path: "freedomHome",
              name: "freedomHome",
              meta: {
                title: "自由交易首页",
              },
              component: () =>
                import("../views/Freedom/childrenView/FreedomHome.vue"),
            },
            {
              path: "freedomSupport",
              name: "freedomSupport",
              meta: {
                title: "服务支持",
              },
              component: () =>
                import("../views/Freedom/childrenView/FreedomSupport.vue"),
            },
          ],
        },
        {
          path: "/translation",
          name: "translation",
          meta: {
            title: "转让交易",
          },
          component: () => import("../views/HomeView.vue"), // 临时使用HomeView作为占位
        },
        {
          path: "/company",
          name: "company",
          meta: {
            title: "企业合作",
          },
          component: () => import("../views/HomeView.vue"), // 临时使用HomeView作为占位
        },
        {
          path: "/exclusive",
          name: "exclusive",
          meta: {
            title: "专属服务",
          },
          component: () => import("../views/HomeView.vue"), // 临时使用HomeView作为占位
        },
        // 拍品详情页面路由
        {
          path: "/auction/detail",
          name: "auctionDetail",
          meta: {
            title: "拍品详情",
          },
          component: () => import("../views/AuctionDetail/index.vue"),
        },
        // 未审核详情页面路由
        {
          path: "/weishenDetail",
          name: "weishenDetail",
          meta: {
            title: "未审核详情",
          },
          component: () => import("../views/WeishenDetail/index.vue"),
        },
        // 公告信息页面路由
        {
          path: "/announcement",
          name: "announcementInfo",
          meta: {
            title: "公告信息",
          },
          component: () => import("../views/AnnouncementInfo/index.vue"),
          children: [
            {
              path: "list",
              name: "announcementInfo-list",
              meta: { title: "公告信息" },
              component: () => import("../views/AnnouncementInfo/childrenView/AnnouncementList.vue"),
            },
            {
              path: "support",
              name: "announcementInfo-support",
              meta: { title: "服务支持" },
              component: () => import("../views/AnnouncementInfo/childrenView/ServiceSupport.vue"),
            },
            {
              path: "detail",
              name: "announcementInfo-detail",
              meta: { title: "公告详情" },
              component: () => import("../views/AnnouncementInfo/childrenView/AnnouncementDetail.vue"),
            },
          ],
        },
        // 资产详情页面路由
        {
          path: "/propertyDetail",
          name: "propertyDetail",
          meta: {
            title: "资产详情",
          },
          component: () =>
            import("../views/Freedom/childrenView/PropertyDetail.vue"),
        },
        // 企业详情页面路由
        {
          path: "/enterpriseDetail",
          name: "enterpriseDetail",
          meta: {
            title: "企业详情",
          },
          component: () => import("../views/EnterpriseDetail/index.vue"),
        },
      ],
    },
  ],
  // 路由滚动行为配置
  scrollBehavior(to, from, savedPosition) {
    // savedPosition 只会在使用浏览器的前进/后退按钮时才可用
    if (savedPosition) {
      // 如果存在 savedPosition（即用户是通过浏览器的前进/后退按钮触发的导航）
      // 则滚动到之前保存的位置
      return {
        ...savedPosition,
        behavior: "smooth", // 添加平滑滚动效果
      };
    } else {
      // 对于所有其他导航，滚动到页面顶部
      return {
        top: 0,
        left: 0,
        behavior: "smooth", // 添加平滑滚动效果
      };
    }
  },
});

export default router;
