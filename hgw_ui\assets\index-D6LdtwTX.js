import{_ as e,S as a}from"./index-CCmno-0X.js";import{b as l,e as s,E as n,g as o,h as i,i as t,j as c}from"./element-plus-BiAL0NdQ.js";import{M as r}from"./Modal-DCQzYcok.js";import{d,c as u,E as v,G as m,b as p,o as f,I as g,$ as b,r as y,a as h,K as k,s as w,ax as _,Q as V,O as C,ad as P,F as U,a1 as x,D as I,C as q}from"./vue-vendor-D6tHD5lA.js";import{u as j}from"./app-stores-CLUCXxRF.js";import{u as N,c as S,d as B}from"./utils-common-PdkFOSu3.js";import"./utils-http-DfEnKTfr.js";import"./crypto-vendor-CkZKNqwc.js";import{v as O,u as T,i as F}from"./uploadUtils-CGtf3Ty6.js";import{U as z}from"./UploadCard-GBXZ_Mqg.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vue-router-C0lzQS1p.js";import"./http-vendor-ztdpVPaQ.js";import"./element-icons-Cfq32zG_.js";import"./vendor-lodash-D0OfQ6x6.js";const A=e(d({__name:"LogoutConfirmModal",props:{modelValue:{type:Boolean},loading:{type:Boolean,default:!1}},emits:["update:modelValue","confirm","cancel"],setup(e,{emit:a}){const l=e,s=a,n=u({get:()=>l.modelValue,set:e=>s("update:modelValue",e)}),o=()=>{s("confirm")},i=()=>{s("cancel"),n.value=!1};return(e,a)=>(f(),v(r,{modelValue:n.value,"onUpdate:modelValue":a[0]||(a[0]=e=>n.value=e),title:"退出登录",width:"400px","confirm-loading":e.loading,"confirm-button-text":"确认退出","cancel-button-text":"取消",onConfirm:o,onCancel:i},{default:m(()=>a[1]||(a[1]=[p("div",{class:"logout-confirm-content"},[p("p",{class:"logout-message"}," 确定要退出登录吗？ "),p("p",{class:"logout-note"}," 退出后需要重新登录才能使用相关功能。 ")],-1)])),_:1,__:[1]},8,["modelValue","confirm-loading"]))}}),[["__scopeId","data-v-b43d2783"]]),L={class:"delete-account-content"},$={class:"button-section"},M=e(d({__name:"DeleteAccountModal",props:{modelValue:{type:Boolean},loading:{type:Boolean,default:!1}},emits:["update:modelValue","confirm","cancel"],setup(e,{emit:a}){const s=e,n=a,o=u({get:()=>s.modelValue,set:e=>n("update:modelValue",e)}),i=()=>{n("confirm")},t=()=>{n("cancel"),o.value=!1};return(e,a)=>{const s=l;return f(),v(r,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value=e),title:"注销账号",width:"658px","show-footer":!1},{default:m(()=>[p("div",L,[a[3]||(a[3]=p("p",{class:"delete-message"}," 您好！若您确认需要注销当前账号，请仔细阅读以下信息： ",-1)),a[4]||(a[4]=p("div",{class:"consequence-section"},[p("h4",{class:"section-title danger"},"一、注销后果"),p("p",{class:"consequence-text danger"}," 账号注销后，系统将永久删除您的个人资料、发布内容、互动记录等所有相关数据，且无法恢复。请慎重考虑身份需要保留的信息。 ")],-1)),a[5]||(a[5]=p("div",{class:"condition-section"},[p("h4",{class:"section-title"},"二、注销条件"),p("ul",{class:"condition-list"},[p("li",null,"账号无未完成订单，未结清款项或其他未处理"),p("li",null,"需通过绑定手机号/邮箱所有权的验证"),p("li",null,"注销后30天内若未重新激活，账号将彻底删除关闭")])],-1)),a[6]||(a[6]=p("div",{class:"special-section"},[p("h4",{class:"section-title"},"三、特别说明"),p("ul",{class:"special-list"},[p("li",null,"注销不影响第三方平台授权（如需解除除单独操作）"),p("li",null,"根据《网络安全法》等法律，我们将继续保留必要信息6个月")])],-1)),a[7]||(a[7]=p("div",{class:"question-section"},[p("h4",{class:"section-title"},"四、如有疑问"),p("p",{class:"question-text"}," 请致电客服(400-XXX-XXXX)或通过官网在线咨询，我们将协助您处理相关事宜。 ")],-1)),a[8]||(a[8]=p("div",{class:"thanks-section"},[p("p",{class:"thanks-text"},"感谢您选择我们的服务，祝您未来一切顺利！")],-1)),p("div",$,[g(s,{class:"confirm-button",onClick:i,plain:"",loading:e.loading},{default:m(()=>a[1]||(a[1]=[b("确定")])),_:1,__:[1]},8,["loading"]),g(s,{class:"cancel-button",type:"primary",onClick:t},{default:m(()=>a[2]||(a[2]=[b("取消")])),_:1,__:[2]})])])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-2799b9d9"]]),X={class:"personal-info"},E={class:"user-header"},D={class:"user-avatar"},R={class:"avatar-bg"},G={class:"info-list"},J={class:"info-item"},K={class:"info-content"},Q={class:"value"},H={class:"info-item"},W={class:"info-content"},Y={class:"value"},Z={class:"info-item"},ee={class:"info-content"},ae={class:"value"},le={class:"info-item"},se={class:"info-content"},ne={class:"value"},oe={class:"info-item"},ie={class:"info-content"},te={class:"value"},ce={class:"info-item"},re={class:"info-content"},de={class:"value"},ue={class:"info-item"},ve={class:"info-content"},me={class:"value"},pe={key:0,class:"role-switch-content"},fe={class:"switch-message"},ge={class:"switch-question"},be={key:1,class:"edit-form"},ye={class:"form-item"},he=e(d({__name:"PersonalInfo",setup(e){const l=y({nickname:"张三",username:"张三",userId:"hg000000001",phone:"131 2345 6789",role:"竞买人",address:"河南省林州市临淇镇政府街林钢公司家属院20号1楼110室",introduction:"暂未填写个人介绍"}),n=y(!1),o=y(!1),i=y(""),t=y(""),c=y(!1),d=y(!1),u=y(!1),v=y(!1),w={nickname:"昵称",username:"用户名",phone:"联系电话",role:"用户身份",address:"地址",introduction:"个人介绍"},_=e=>{i.value=e,"role"!==e&&(t.value=l.value[e]),n.value=!0},V=async()=>{c.value=!0;try{if(await new Promise(e=>setTimeout(e,1e3)),"role"===i.value){const e="竞买人"===l.value.role?"处置企业":"竞买人";l.value.role=e}else i.value&&t.value.trim()&&(l.value[i.value]=t.value.trim());n.value=!1}catch(e){}finally{c.value=!1}},C=()=>{n.value=!1,t.value="",i.value=""},P=()=>{o.value=!0},U=async()=>{d.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),localStorage.removeItem("token"),localStorage.removeItem("userInfo"),o.value=!1}catch(e){}finally{d.value=!1}},x=()=>{o.value=!1},I=()=>{u.value=!0},q=async()=>{v.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),u.value=!1}catch(e){}finally{v.value=!1}},j=()=>{u.value=!1};return(e,y)=>{const N=s;return f(),h("div",X,[p("div",E,[p("div",D,[p("div",R,[g(a,{iconName:"user",className:"avatar-icon"})]),y[10]||(y[10]=p("span",{class:"phone-display"},"已实名认证 131 ****6789",-1))])]),p("div",G,[p("div",J,[p("div",K,[y[11]||(y[11]=p("div",{class:"label"},"昵称",-1)),p("div",Q,k(l.value.nickname),1)]),p("button",{class:"edit-btn",onClick:y[0]||(y[0]=e=>_("nickname"))},"编辑")]),p("div",H,[p("div",W,[y[12]||(y[12]=p("div",{class:"label"},"用户名",-1)),p("div",Y,k(l.value.username),1)]),p("button",{class:"edit-btn",onClick:y[1]||(y[1]=e=>_("username"))},"编辑")]),p("div",Z,[p("div",ee,[y[13]||(y[13]=p("div",{class:"label"},"用户ID",-1)),p("div",ae,k(l.value.userId),1)])]),p("div",le,[p("div",se,[y[14]||(y[14]=p("div",{class:"label"},"联系电话",-1)),p("div",ne,k(l.value.phone),1)]),p("button",{class:"edit-btn",onClick:y[2]||(y[2]=e=>_("phone"))},"编辑")]),p("div",oe,[p("div",ie,[y[15]||(y[15]=p("div",{class:"label"},"用户身份",-1)),p("div",te,k(l.value.role),1)]),p("button",{class:"edit-btn",onClick:y[3]||(y[3]=e=>_("role"))},"切换")]),p("div",ce,[p("div",re,[y[16]||(y[16]=p("div",{class:"label"},"地址",-1)),p("div",de,k(l.value.address),1)]),p("button",{class:"edit-btn",onClick:y[4]||(y[4]=e=>_("address"))},"编辑")]),p("div",ue,[p("div",ve,[y[17]||(y[17]=p("div",{class:"label"},"个人介绍",-1)),p("div",me,k(l.value.introduction),1)]),p("button",{class:"edit-btn",onClick:y[5]||(y[5]=e=>_("introduction"))},"编辑")])]),p("div",{class:"bottom-actions"},[p("button",{class:"logout-btn",onClick:P},"退出登录"),p("button",{class:"delete-account-btn",onClick:I},"注销账号")]),g(r,{modelValue:n.value,"onUpdate:modelValue":y[7]||(y[7]=e=>n.value=e),title:"role"===i.value?"切换用户身份":`编辑${w[i.value]}`,width:"500px","confirm-loading":c.value,onConfirm:V,onCancel:C},{default:m(()=>["role"===i.value?(f(),h("div",pe,[p("p",fe,[y[18]||(y[18]=b(" 您当前的身份是：")),p("strong",null,k(l.value.role),1)]),p("p",ge,[y[19]||(y[19]=b(" 是否要切换为：")),p("strong",null,k("竞买人"===l.value.role?"处置企业":"竞买人"),1),y[20]||(y[20]=b("？ "))]),y[21]||(y[21]=p("p",{class:"switch-note"}," 注意：切换身份后，您的权限和功能将发生变化。 ",-1))])):(f(),h("div",be,[p("div",ye,[g(N,{modelValue:t.value,"onUpdate:modelValue":y[6]||(y[6]=e=>t.value=e),placeholder:`请输入${w[i.value]}`,type:"introduction"===i.value?"textarea":"text",rows:"introduction"===i.value?4:void 0,"show-word-limit":"",clearable:""},null,8,["modelValue","placeholder","type","rows"])])]))]),_:1},8,["modelValue","title","confirm-loading"]),g(A,{modelValue:o.value,"onUpdate:modelValue":y[8]||(y[8]=e=>o.value=e),loading:d.value,onConfirm:U,onCancel:x},null,8,["modelValue","loading"]),g(M,{modelValue:u.value,"onUpdate:modelValue":y[9]||(y[9]=e=>u.value=e),loading:v.value,onConfirm:q,onCancel:j},null,8,["modelValue","loading"])])}}}),[["__scopeId","data-v-9f545bcf"]]),ke={class:"account-security"},we={class:"user-header"},_e={class:"user-avatar"},Ve={class:"avatar-bg"},Ce={class:"certification-info"},Pe={class:"phone-display"},Ue={class:"certification-status"},xe={class:"cert-item"},Ie={class:"cert-item"},qe={class:"security-list"},je={class:"security-item"},Ne={class:"security-item"},Se={class:"security-content"},Be={class:"value"},Oe={class:"password-form"},Te={class:"form-item"},Fe={class:"form-item"},ze={class:"form-item"},Ae={class:"phone-form"},Le={class:"form-item"},$e={class:"phone-display"},Me={class:"phone-number"},Xe={class:"form-item"},Ee={class:"verify-input"},De={class:"form-item"},Re=e(d({__name:"AccountSecurity",setup(e){const o=j(),i=y(!1),t=y(!1),c=y(!1),d=y(!1),v=y({enterqiye:0,enteruser:0}),V=u(()=>{var e;return(null==(e=o.userInfo)?void 0:e.mobile)||""}),C=u(()=>1===v.value.enteruser),P=u(()=>1===v.value.enterqiye),U=y({currentPassword:"",newPassword:"",confirmPassword:""}),x=y({currentPhone:"131 2345 6789",verifyCode:"",newPhone:""}),I=y(0),q=y(null),B=async()=>{var e;if(U.value.newPassword===U.value.confirmPassword)if(U.value.newPassword.length<6)n.error("密码长度至少6位");else if(null==(e=o.userInfo)?void 0:e.id){c.value=!0;try{await S.changePassword({jiu_password:U.value.currentPassword,password:U.value.newPassword,member_id:o.userInfo.id}),i.value=!1,n.success("密码修改成功")}catch(a){n.error("密码修改失败")}finally{c.value=!1}}else n.error("用户信息不完整");else n.error("两次输入的新密码不一致")},O=()=>{i.value=!1},T=async()=>{if(!(I.value>0))try{await new Promise(e=>setTimeout(e,500)),I.value=60,q.value=setInterval(()=>{I.value--,I.value<=0&&(clearInterval(q.value),q.value=null)},1e3),n.success("验证码已发送")}catch(e){n.error("验证码发送失败")}},F=async()=>{if(/^1[3-9]\d{9}$/.test(x.value.newPhone.replace(/\s/g,"")))if(x.value.verifyCode){d.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),t.value=!1,n.success("联系方式修改成功")}catch(e){n.error("联系方式修改失败")}finally{d.value=!1}}else n.info("请输入验证码");else n.info("请输入正确的手机号格式")},z=()=>{t.value=!1,q.value&&(clearInterval(q.value),q.value=null,I.value=0)},A=y(!1),L=y(!1),$=async()=>{L.value=!0;try{await new Promise(e=>setTimeout(e,2e3))}catch(e){}finally{L.value=!1,A.value=!1}},X=()=>{A.value=!1};return w(()=>{(async()=>{var e;if(null==(e=o.userInfo)?void 0:e.id)try{const e=await N.checkCertification({member_id:o.userInfo.id});200===e.code&&(v.value={enterqiye:e.data.enterqiye||0,enteruser:e.data.enteruser||0})}catch(a){}})()}),(e,n)=>{const o=s,u=l;return f(),h("div",ke,[p("div",we,[p("div",_e,[p("div",Ve,[g(a,{iconName:"user",className:"avatar-icon"})]),p("div",Ce,[p("div",Pe,k(V.value?V.value.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):""),1),p("div",Ue,[p("span",xe,"个人认证: "+k(C.value?"已认证":"未认证"),1),p("span",Ie,"企业认证: "+k(P.value?"已认证":"未认证"),1)])])])]),p("div",qe,[p("div",je,[n[9]||(n[9]=_('<div class="security-content" data-v-9a087e79><div class="label" data-v-9a087e79><span class="label-text" data-v-9a087e79>登录密码</span><span class="tips" data-v-9a087e79>(互联网账号存在被盗风险，建议您定期更改密码以保护账户安全)</span></div><div class="value" data-v-9a087e79> 已设置登录密码，建议您定期更改密码以保护账户安全 </div></div>',1)),p("button",{class:"edit-btn",onClick:n[0]||(n[0]=e=>(i.value=!0,void(U.value={currentPassword:"",newPassword:"",confirmPassword:""})))},"编辑")]),p("div",Ne,[p("div",Se,[n[10]||(n[10]=p("div",{class:"label"},[p("span",{class:"label-text"},"联系方式"),p("span",{class:"tips"},"(建议您提供有效联系方式，随时随地获得商机)")],-1)),p("div",Be,k(V.value||"未绑定手机号"),1)])])]),g(r,{modelValue:i.value,"onUpdate:modelValue":n[4]||(n[4]=e=>i.value=e),title:"修改登录密码",width:"658px","confirm-loading":c.value,"confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:B,onCancel:O},{default:m(()=>[p("div",Oe,[p("div",Te,[n[11]||(n[11]=p("label",{class:"form-label"},"当前密码",-1)),g(o,{modelValue:U.value.currentPassword,"onUpdate:modelValue":n[1]||(n[1]=e=>U.value.currentPassword=e),type:"password",placeholder:"输入当前的密码","show-password":"",clearable:""},null,8,["modelValue"])]),p("div",Fe,[n[12]||(n[12]=p("label",{class:"form-label"},"新密码",-1)),g(o,{modelValue:U.value.newPassword,"onUpdate:modelValue":n[2]||(n[2]=e=>U.value.newPassword=e),type:"password",placeholder:"请输入8位以上的新密码，需包含大小写字母、数字及特殊字符","show-password":"",clearable:""},null,8,["modelValue"])]),p("div",ze,[n[13]||(n[13]=p("label",{class:"form-label"},"确认新密码",-1)),g(o,{modelValue:U.value.confirmPassword,"onUpdate:modelValue":n[3]||(n[3]=e=>U.value.confirmPassword=e),type:"password",placeholder:"请再次确认新密码","show-password":"",clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue","confirm-loading"]),g(r,{modelValue:t.value,"onUpdate:modelValue":n[7]||(n[7]=e=>t.value=e),title:"修改联系方式",width:"658px","confirm-loading":d.value,"confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:F,onCancel:z},{default:m(()=>[p("div",Ae,[p("div",Le,[n[15]||(n[15]=p("label",{class:"form-label"},"当前手机号",-1)),p("div",$e,[n[14]||(n[14]=p("span",{class:"country-code"},"+86",-1)),p("span",Me,k(x.value.currentPhone),1)])]),p("div",Xe,[n[16]||(n[16]=p("label",{class:"form-label"},"验证码",-1)),p("div",Ee,[g(o,{modelValue:x.value.verifyCode,"onUpdate:modelValue":n[5]||(n[5]=e=>x.value.verifyCode=e),placeholder:"请输入验证码",clearable:""},null,8,["modelValue"]),g(u,{type:"primary",disabled:I.value>0,onClick:T,class:"verify-btn"},{default:m(()=>[b(k(I.value>0?`${I.value}s`:"获取验证码"),1)]),_:1},8,["disabled"])])]),p("div",De,[n[17]||(n[17]=p("label",{class:"form-label"},"新手机号",-1)),g(o,{modelValue:x.value.newPhone,"onUpdate:modelValue":n[6]||(n[6]=e=>x.value.newPhone=e),placeholder:"请输入新手机号",clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue","confirm-loading"]),g(M,{modelValue:A.value,"onUpdate:modelValue":n[8]||(n[8]=e=>A.value=e),loading:L.value,onConfirm:$,onCancel:X},null,8,["modelValue","loading"])])}}}),[["__scopeId","data-v-9a087e79"]]),Ge={class:"personal-verification"},Je={class:"form-section"},Ke={class:"form-item"},Qe={class:"form-value"},He={class:"form-item"},We={class:"form-value"},Ye={class:"form-item"},Ze={class:"form-value"},ea={class:"form-item"},aa={class:"form-value"},la={class:"form-item"},sa={class:"form-value"},na={class:"form-item"},oa={class:"form-value"},ia={class:"form-item"},ta={class:"form-value"},ca={class:"upload-section"},ra={class:"upload-group"},da=e(d({__name:"PersonalVerification",props:{areaOptions:{},recycleOptions:{}},emits:["save","submit"],setup(e,{expose:a,emit:l}){const s=l,c=j(),r=V({cnname:"",cardnum:"",gerenyinhang:"",gerenkahao:"",biaoqian:[],province:"",city:"",district:"",selectedArea:[],cardpicz:"",cardpicf:"",idCardFront:null,idCardBack:null,member_id:c.userInfo?c.userInfo.id:0}),d=e=>{if(Array.isArray(e)&&3===e.length){const[a,l,s]=e;r.province=a||"",r.city=l||"",r.district=s||""}},u=async e=>{try{O(e),r.idCardFront=e;const a=await T(e);r.cardpicz=a,n.success("身份证正面上传成功")}catch(a){n.error("身份证正面上传失败")}},y=async e=>{try{O(e),r.idCardBack=e;const a=await T(e);r.cardpicf=a,n.success("身份证反面上传成功")}catch(a){n.error("身份证反面上传失败")}};return a({handleSave:async()=>{var e;try{if(!r.cnname||!r.cardnum)return void n.warning("请填写完整的个人信息");const a={cnname:r.cnname,cardnum:r.cardnum,gerenyinhang:r.gerenyinhang,gerenkahao:r.gerenkahao,biaoqian:r.biaoqian.join(","),province:r.province,city:r.city,district:r.district,cardpicz:r.cardpicz,cardpicf:r.cardpicf,status:2,type:1,member_id:(null==(e=c.userInfo)?void 0:e.id)||0};s("save",a)}catch(a){n.error("保存失败")}},handleSubmit:async()=>{try{if(!(r.cnname&&r.cardnum&&r.cardpicz&&r.cardpicf))return void n.warning("请填写完整的个人信息并上传身份证件");const e={cnname:r.cnname,cardnum:r.cardnum,gerenyinhang:r.gerenyinhang,gerenkahao:r.gerenkahao,biaoqian:r.biaoqian.join(","),province:r.province,city:r.city,district:r.district,cardpicz:r.cardpicz,cardpicf:r.cardpicf,status:3,type:1};s("submit",e)}catch(e){n.error("提交失败")}}}),w(()=>{(async()=>{var e;try{const a=null==(e=c.userInfo)?void 0:e.id;if(!a)return;const l=await N.getCertificationInfo({member_id:a,type:1});if(1===l.code&&l.data){const e=l.data;r.cnname=e.cnname||"",r.cardnum=e.cardnum||"",r.gerenyinhang=e.gerenyinhang||"",r.gerenkahao=e.gerenkahao||"",r.biaoqian=e.biaoqian?e.biaoqian.split(","):[],r.province=e.province||"",r.city=e.city||"",r.district=e.district||"",r.cardpicz=e.cardpicz||"",r.cardpicf=e.cardpicf||"",r.selectedArea=[e.province.toString(),e.city.toString(),e.district.toString()]}}catch(a){}})()}),(e,a)=>{const l=o,s=i,n=t;return f(),h("div",Ge,[p("div",Je,[p("div",Ke,[a[6]||(a[6]=p("label",{class:"form-label"},"姓名",-1)),p("div",Qe,[C(p("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>r.cnname=e),type:"text",class:"form-input",placeholder:"请输入姓名"},null,512),[[P,r.cnname]])])]),p("div",He,[a[7]||(a[7]=p("label",{class:"form-label"},"身份证号",-1)),p("div",We,[C(p("input",{"onUpdate:modelValue":a[1]||(a[1]=e=>r.cardnum=e),type:"text",class:"form-input",placeholder:"请输入身份证号"},null,512),[[P,r.cardnum]])])]),p("div",Ye,[a[8]||(a[8]=p("label",{class:"form-label"},"开户行",-1)),p("div",Ze,[C(p("input",{"onUpdate:modelValue":a[2]||(a[2]=e=>r.gerenyinhang=e),type:"text",class:"form-input",placeholder:"请输入开户行"},null,512),[[P,r.gerenyinhang]])])]),p("div",ea,[a[9]||(a[9]=p("label",{class:"form-label"},"银行账号",-1)),p("div",aa,[C(p("input",{"onUpdate:modelValue":a[3]||(a[3]=e=>r.gerenkahao=e),type:"text",class:"form-input",placeholder:"请输入银行账号"},null,512),[[P,r.gerenkahao]])])]),p("div",la,[a[10]||(a[10]=p("label",{class:"form-label"},"回收种类",-1)),p("div",sa,[g(s,{modelValue:r.biaoqian,"onUpdate:modelValue":a[4]||(a[4]=e=>r.biaoqian=e),class:"checkbox-group"},{default:m(()=>[(f(!0),h(U,null,x(e.recycleOptions,e=>(f(),v(l,{key:e.value,value:e.value,class:"checkbox-item"},{default:m(()=>[b(k(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])]),p("div",na,[a[11]||(a[11]=p("label",{class:"form-label"},"所属地区",-1)),p("div",oa,[g(n,{modelValue:r.selectedArea,"onUpdate:modelValue":a[5]||(a[5]=e=>r.selectedArea=e),options:e.areaOptions,props:{expandTrigger:"hover"},placeholder:"请选择省市区",class:"area-cascader",onChange:d},null,8,["modelValue","options"])])]),p("div",ia,[a[13]||(a[13]=p("label",{class:"form-label"},"上传证件",-1)),p("div",ta,[p("div",ca,[a[12]||(a[12]=p("p",{class:"upload-description"}," 请上传身份证件，要求为彩色，最多可上传2个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。 ",-1)),p("div",ra,[g(z,{uploadText:"身份证正面",backgroundImage:"@/assets/icons/upload/idcard-front.svg",imageUrl:`${r.cardpicz}`,onUpload:u},null,8,["imageUrl"]),g(z,{uploadText:"身份证反面",backgroundImage:"@/assets/icons/upload/idcard-reverse.svg",imageUrl:r.cardpicf,onUpload:y},null,8,["imageUrl"])])])])])])])}}}),[["__scopeId","data-v-32f37f9a"]]),ua={class:"enterprise-verification"},va={class:"form-section"},ma={class:"form-item"},pa={class:"form-value"},fa={class:"form-item"},ga={class:"form-value"},ba={class:"form-item"},ya={class:"form-value"},ha={class:"form-item"},ka={class:"form-value"},wa={class:"form-item"},_a={class:"form-value"},Va={class:"form-item"},Ca={class:"form-value"},Pa={class:"form-item"},Ua={class:"form-value"},xa={class:"form-item"},Ia={class:"form-value"},qa={class:"form-item"},ja={class:"form-value"},Na={class:"form-item"},Sa={class:"form-value"},Ba={class:"form-item"},Oa={class:"form-value"},Ta={class:"form-item"},Fa={class:"form-value"},za={class:"upload-section"},Aa={class:"form-item"},La={class:"form-value"},$a={class:"form-item"},Ma={class:"form-value"},Xa={class:"form-item"},Ea={class:"form-value"},Da={class:"upload-section"},Ra={class:"upload-group"},Ga=e(d({__name:"EnterpriseVerification",props:{areaOptions:{},recycleOptions:{}},emits:["save","submit"],setup(e,{expose:a,emit:l}){const s=l,c=j(),r=V({companyName:"",creditCode:"",contactPerson:"",contactPhone:"",address:"",kpdianhua:"",kaihuhang:"",gonghu:"",hanghao:"",biaoqian:[],selectedArea:[],province:"",city:"",district:"",legalPersonName:"",legalPersonIdCard:"",businessLicense:null,businessLicenseUrl:"",legalPersonFront:null,legalPersonFrontUrl:"",legalPersonBack:null,legalPersonBackUrl:"",member_id:c.userInfo?c.userInfo.id:0}),d=async e=>{try{O(e),r.businessLicense=e;const a=await T(e);r.businessLicenseUrl=a,n.success("营业执照上传成功")}catch(a){n.error("营业执照上传失败")}},u=async e=>{try{O(e),r.legalPersonFront=e;const a=await T(e);r.legalPersonFrontUrl=a,n.success("法人身份证正面上传成功")}catch(a){n.error("法人身份证正面上传失败")}},y=async e=>{try{O(e),r.legalPersonBack=e;const a=await T(e);r.legalPersonBackUrl=a,n.success("法人身份证反面上传成功")}catch(a){n.error("法人身份证反面上传失败")}},_=e=>{e&&3===e.length?(r.province=e[0],r.city=e[1],r.district=e[2]):(r.province="",r.city="",r.district="")};return a({handleSave:async()=>{var e;try{if(!r.companyName||!r.creditCode)return void n.warning("请填写完整的企业信息");const a={qiyemingcheng:r.companyName,xinyongdaima:r.creditCode,lianxiren:r.contactPerson,lianxidianhua:r.contactPhone,address:r.address,kpdianhua:r.kpdianhua,kaihuhang:r.kaihuhang,gonghu:r.gonghu,hanghao:r.hanghao,biaoqian:r.biaoqian.join(","),province:r.province,city:r.city,district:r.district,fr_name:r.legalPersonName,fr_cardnum:r.legalPersonIdCard,qiyepic:r.businessLicenseUrl,fr_cardpicz:r.legalPersonFrontUrl,fr_cardpicf:r.legalPersonBackUrl,status:2,type:2,member_id:(null==(e=c.userInfo)?void 0:e.id)||0};s("save",a)}catch(a){n.error("保存失败")}},handleSubmit:async()=>{var e;try{if(!(r.companyName&&r.creditCode&&r.businessLicenseUrl&&r.legalPersonFrontUrl&&r.legalPersonBackUrl))return void n.warning("请填写完整的企业信息并上传相关证件");const a={qiyemingcheng:r.companyName,xinyongdaima:r.creditCode,lianxiren:r.contactPerson,lianxidianhua:r.contactPhone,address:r.address,kpdianhua:r.kpdianhua,kaihuhang:r.kaihuhang,gonghu:r.gonghu,hanghao:r.hanghao,biaoqian:r.biaoqian.join(","),province:r.province,city:r.city,district:r.district,fr_name:r.legalPersonName,fr_cardnum:r.legalPersonIdCard,qiyepic:r.businessLicenseUrl,fr_cardpicz:r.legalPersonFrontUrl,fr_cardpicf:r.legalPersonBackUrl,status:3,type:2,member_id:(null==(e=c.userInfo)?void 0:e.id)||0};s("submit",a)}catch(a){n.error("提交失败")}}}),w(()=>{(async()=>{var e;try{const a=null==(e=c.userInfo)?void 0:e.id;if(!a)return;const l=await N.getCertificationInfo({member_id:a,type:2});if(1===l.code&&l.data){const e=l.data;r.companyName=e.qiyemingcheng||"",r.creditCode=e.xinyongdaima||"",r.contactPerson=e.lianxiren||"",r.contactPhone=e.lianxidianhua||"",r.address=e.address||"",r.kpdianhua=e.kpdianhua||"",r.kaihuhang=e.kaihuhang||"",r.gonghu=e.gonghu||"",r.hanghao=e.hanghao||"",e.biaoqian&&(r.biaoqian=e.biaoqian.split(",")),r.legalPersonName=e.fr_name||"",r.legalPersonIdCard=e.fr_cardnum||"",r.businessLicenseUrl=e.qiyepic||"",r.legalPersonFrontUrl=e.fr_cardpicz||"",r.legalPersonBackUrl=e.fr_cardpicf||"",r.selectedArea=[e.province.toString(),e.city.toString(),e.district.toString()]}}catch(a){}})()}),(e,a)=>{const l=o,s=i,n=t;return f(),h("div",ua,[p("div",va,[p("div",ma,[a[13]||(a[13]=p("label",{class:"form-label"},"企业名称",-1)),p("div",pa,[C(p("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>r.companyName=e),type:"text",class:"form-input",placeholder:"请输入企业名称"},null,512),[[P,r.companyName]])])]),p("div",fa,[a[14]||(a[14]=p("label",{class:"form-label"},"统一社会信用代码",-1)),p("div",ga,[C(p("input",{"onUpdate:modelValue":a[1]||(a[1]=e=>r.creditCode=e),type:"text",class:"form-input",placeholder:"请输入统一社会信用代码"},null,512),[[P,r.creditCode]])])]),p("div",ba,[a[15]||(a[15]=p("label",{class:"form-label"},"业务联系人",-1)),p("div",ya,[C(p("input",{"onUpdate:modelValue":a[2]||(a[2]=e=>r.contactPerson=e),type:"text",class:"form-input",placeholder:"请输入业务联系人"},null,512),[[P,r.contactPerson]])])]),p("div",ha,[a[16]||(a[16]=p("label",{class:"form-label"},"联系电话",-1)),p("div",ka,[C(p("input",{"onUpdate:modelValue":a[3]||(a[3]=e=>r.contactPhone=e),type:"text",class:"form-input",placeholder:"请输入联系电话"},null,512),[[P,r.contactPhone]])])]),p("div",wa,[a[17]||(a[17]=p("label",{class:"form-label"},"公司地址",-1)),p("div",_a,[C(p("input",{"onUpdate:modelValue":a[4]||(a[4]=e=>r.address=e),type:"text",class:"form-input",placeholder:"请输入公司地址"},null,512),[[P,r.address]])])]),p("div",Va,[a[18]||(a[18]=p("label",{class:"form-label"},"开票电话",-1)),p("div",Ca,[C(p("input",{"onUpdate:modelValue":a[5]||(a[5]=e=>r.kpdianhua=e),type:"text",class:"form-input",placeholder:"请输入开票电话"},null,512),[[P,r.kpdianhua]])])]),p("div",Pa,[a[19]||(a[19]=p("label",{class:"form-label"},"开户行",-1)),p("div",Ua,[C(p("input",{"onUpdate:modelValue":a[6]||(a[6]=e=>r.kaihuhang=e),type:"text",class:"form-input",placeholder:"请输入开户行"},null,512),[[P,r.kaihuhang]])])]),p("div",xa,[a[20]||(a[20]=p("label",{class:"form-label"},"公司账户",-1)),p("div",Ia,[C(p("input",{"onUpdate:modelValue":a[7]||(a[7]=e=>r.gonghu=e),type:"text",class:"form-input",placeholder:"请输入公司账户"},null,512),[[P,r.gonghu]])])]),p("div",qa,[a[21]||(a[21]=p("label",{class:"form-label"},"行号",-1)),p("div",ja,[C(p("input",{"onUpdate:modelValue":a[8]||(a[8]=e=>r.hanghao=e),type:"text",class:"form-input",placeholder:"请输入行号"},null,512),[[P,r.hanghao]])])]),p("div",Na,[a[22]||(a[22]=p("label",{class:"form-label"},"回收种类",-1)),p("div",Sa,[g(s,{modelValue:r.biaoqian,"onUpdate:modelValue":a[9]||(a[9]=e=>r.biaoqian=e),class:"checkbox-group"},{default:m(()=>[(f(!0),h(U,null,x(e.recycleOptions,e=>(f(),v(l,{key:e.value,value:e.value,class:"checkbox-item"},{default:m(()=>[b(k(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])]),p("div",Ba,[a[23]||(a[23]=p("label",{class:"form-label"},"所属地区",-1)),p("div",Oa,[g(n,{modelValue:r.selectedArea,"onUpdate:modelValue":a[10]||(a[10]=e=>r.selectedArea=e),options:e.areaOptions,props:{expandTrigger:"hover"},placeholder:"请选择省市区",class:"area-cascader",onChange:_},null,8,["modelValue","options"])])]),p("div",Ta,[a[25]||(a[25]=p("label",{class:"form-label"},"营业执照",-1)),p("div",Fa,[p("div",za,[a[24]||(a[24]=p("p",{class:"upload-description"}," 请上传营业执照副本，要求为彩色，最多可上传1个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。 ",-1)),g(z,{uploadText:"营业执照",backgroundImage:"@/assets/icons/upload/business-license.svg",imageUrl:r.businessLicenseUrl,onUpload:d},null,8,["imageUrl"])])])]),p("div",Aa,[a[26]||(a[26]=p("label",{class:"form-label"},"法人姓名",-1)),p("div",La,[C(p("input",{"onUpdate:modelValue":a[11]||(a[11]=e=>r.legalPersonName=e),type:"text",class:"form-input",placeholder:"请输入法人姓名"},null,512),[[P,r.legalPersonName]])])]),p("div",$a,[a[27]||(a[27]=p("label",{class:"form-label"},"法人身份证号",-1)),p("div",Ma,[C(p("input",{"onUpdate:modelValue":a[12]||(a[12]=e=>r.legalPersonIdCard=e),type:"text",class:"form-input",placeholder:"请输入法人身份证号"},null,512),[[P,r.legalPersonIdCard]])])]),p("div",Xa,[a[29]||(a[29]=p("label",{class:"form-label"},"上传法人证件",-1)),p("div",Ea,[p("div",Da,[a[28]||(a[28]=p("p",{class:"upload-description"}," 请上传法人证件，要求为彩色，最多可上传2个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。 ",-1)),p("div",Ra,[g(z,{uploadText:"身份证正面",backgroundImage:"@/assets/icons/upload/idcard-front.svg",imageUrl:r.legalPersonFrontUrl,onUpload:u},null,8,["imageUrl"]),g(z,{uploadText:"身份证反面",backgroundImage:"@/assets/icons/upload/idcard-reverse.svg",imageUrl:r.legalPersonBackUrl,onUpload:y},null,8,["imageUrl"])])])])])])])}}}),[["__scopeId","data-v-311aa2b3"]]),Ja={class:"verification-container"},Ka={class:"verification-nav"},Qa={class:"verification-content"},Ha=e(d({__name:"Verification",setup(e){const a=y(2),l=y([]),s=y([{value:"1",label:"物资设备"},{value:"2",label:"机动车"},{value:"3",label:"房产"},{value:"4",label:"土地"},{value:"5",label:"其他"}]),o=y(),i=y(),t=e=>{a.value=e},c=async e=>{try{const a=await N.uploadCertification(e);1===a.code?n.success("保存成功"):n.error(a.msg||"保存失败")}catch(a){n.error("保存失败")}},r=async e=>{try{const a=await N.uploadCertification(e);200===a.code?n.success("提交审核成功"):n.error(a.msg||"提交失败")}catch(a){n.error("提交失败")}},d=async e=>{try{const a=await N.uploadCertification(e);1===a.code?n.success("保存成功"):n.error(a.msg||"保存失败")}catch(a){n.error("保存失败")}},u=async e=>{try{const a=await N.uploadCertification(e);200===a.code?n.success("提交审核成功"):n.error(a.msg||"提交失败")}catch(a){n.error("提交失败")}},m=()=>{var e,l;1===a.value?null==(e=o.value)||e.handleSave():null==(l=i.value)||l.handleSave()},g=()=>{var e,l;1===a.value?null==(e=o.value)||e.handleSubmit():null==(l=i.value)||l.handleSubmit()};return w(()=>{l.value=F()}),(e,n)=>(f(),h("div",Ja,[p("div",Ka,[p("div",{class:I(["nav-tab enterprise-nav-tab",{active:2===a.value}]),onClick:n[0]||(n[0]=e=>t(2))}," 企业认证 ",2),p("div",{class:I(["nav-tab personal-nav-tab",{active:1===a.value}]),onClick:n[1]||(n[1]=e=>t(1))}," 个人认证 ",2)]),p("div",Qa,[2===a.value?(f(),v(Ga,{key:0,ref_key:"enterpriseVerificationRef",ref:i,areaOptions:l.value,recycleOptions:s.value,onSave:d,onSubmit:u},null,8,["areaOptions","recycleOptions"])):q("",!0),1===a.value?(f(),v(da,{key:1,ref_key:"personalVerificationRef",ref:o,areaOptions:l.value,recycleOptions:s.value,onSave:c,onSubmit:r},null,8,["areaOptions","recycleOptions"])):q("",!0)]),p("div",{class:"action-buttons"},[p("button",{class:"save-btn",onClick:m},"保存"),p("button",{class:"submit-btn",onClick:g},"提交审核")])]))}}),[["__scopeId","data-v-852f5a87"]]),Wa={class:"invoice-container"},Ya={class:"invoice-nav"},Za={class:"invoice-content"},el={key:0,class:"form-section"},al={class:"form-item"},ll={class:"form-value"},sl={key:1,class:"form-section"},nl=e(d({__name:"Invoice",setup(e){const a=y("enterprise"),l=V({companyName:""}),s=e=>{a.value=e},o=()=>{n.success("保存成功")},i=()=>{"enterprise"===a.value?l.companyName?n.success("提交审核成功"):n.warning("请填写企业名称"):n.info("个人认证功能开发中...")};return(e,n)=>(f(),h("div",Wa,[p("div",Ya,[p("div",{class:I(["nav-tab",{active:"enterprise"===a.value}]),onClick:n[0]||(n[0]=e=>s("enterprise"))}," 企业认证 ",2),p("div",{class:I(["nav-tab",{active:"personal"===a.value}]),onClick:n[1]||(n[1]=e=>s("personal"))}," 个人认证 ",2)]),p("div",Za,["enterprise"===a.value?(f(),h("div",el,[p("div",al,[n[3]||(n[3]=p("label",{class:"form-label"},"企业名称",-1)),p("div",ll,[C(p("input",{"onUpdate:modelValue":n[2]||(n[2]=e=>l.companyName=e),type:"text",class:"form-input",placeholder:"请输入企业名称"},null,512),[[P,l.companyName]])])])])):q("",!0),"personal"===a.value?(f(),h("div",sl,n[4]||(n[4]=[p("div",{class:"empty-state"},[p("p",{class:"empty-text"},"个人认证功能开发中...")],-1)]))):q("",!0)]),p("div",{class:"action-buttons"},[p("button",{class:"save-btn",onClick:o},"保存"),p("button",{class:"submit-btn",onClick:i},"提交审核")])]))}}),[["__scopeId","data-v-cd950ef4"]]),ol={class:"followed-companies"},il={class:"header-section"},tl={class:"follow-count"},cl={class:"count"},rl={class:"companies-list"},dl={class:"company-avatar"},ul=["src","alt"],vl={class:"company-info"},ml={class:"company-name"},pl={class:"company-description"},fl=["onClick"],gl=e(d({__name:"FollowedCompanies",setup(e){const a=j();y("");const l=y([]);w(()=>{s()});const s=async()=>{var e;const s=await N.getMyFavoriteCompanies({member_id:null==(e=a.userInfo)?void 0:e.id,page:1,type:1});1===s.code&&(l.value=s.data.data)};return(e,o)=>(f(),h("div",ol,[p("div",il,[p("div",tl,[p("span",cl,k(l.value.length),1),o[0]||(o[0]=p("span",{class:"text"},"关注企业",-1))])]),p("div",rl,[(f(!0),h(U,null,x(l.value,(e,o)=>(f(),h("div",{class:I(["company-item",{"no-border":o===l.value.length-1}]),key:e.id},[p("div",dl,[p("img",{src:`https://huigupaimai.oss-cn-beijing.aliyuncs.com/${e.qiye.qiyelogo}`,alt:e.qiye.qiyemingcheng,class:"avatar-img"},null,8,ul)]),p("div",vl,[p("div",ml,k(e.qiye.qiyemingcheng),1),p("div",pl,k(e.qiye.qiyephone),1)]),p("button",{class:"followed-btn",onClick:l=>(async e=>{var l;await c.confirm("确定取消关注吗？","取消关注",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&1===(await B.favoriteCompany({member_id:null==(l=a.userInfo)?void 0:l.id,qiye_id:e,isquxiao:0,type:1})).code&&(n.success("取消关注成功"),s())})(e.qiye_id)}," 取消关注 ",8,fl)],2))),128))])]))}}),[["__scopeId","data-v-42e2fac5"]]),bl={class:"profile-container"},yl={class:"profile-sidebar"},hl={class:"sidebar-nav"},kl=["onClick"],wl={class:"nav-text"},_l={class:"profile-content"},Vl=e(d({__name:"index",setup(e){const l=y("security"),s=[{key:"security",label:"账号安全",icon:"login-data-security"},{key:"verification",label:"实名认证",icon:"login-data-real-name"},{key:"follow",label:"关注企业",icon:"login-data-attention"}];return(e,n)=>(f(),h("div",bl,[p("div",yl,[n[0]||(n[0]=p("div",{class:"sidebar-title"},"我的资料",-1)),n[1]||(n[1]=p("div",{class:"sidebar-divider"},null,-1)),p("div",hl,[(f(),h(U,null,x(s,e=>p("div",{key:e.key,class:I(["nav-item",{active:l.value===e.key}]),onClick:a=>{return s=e.key,void(l.value=s);var s}},[g(a,{iconName:e.icon,class:"nav-icon"},null,8,["iconName"]),p("span",wl,k(e.label),1)],10,kl)),64))])]),p("div",_l,["personal"===l.value?(f(),v(he,{key:0})):q("",!0),"security"===l.value?(f(),v(Re,{key:1})):q("",!0),"verification"===l.value?(f(),v(Ha,{key:2})):q("",!0),"invoice"===l.value?(f(),v(nl,{key:3})):q("",!0),"follow"===l.value?(f(),v(gl,{key:4})):q("",!0)])]))}}),[["__scopeId","data-v-29f9f33b"]]);export{Vl as default};
