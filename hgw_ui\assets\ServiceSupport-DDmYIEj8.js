import{e as F,b as Q}from"./element-plus-DZBbDeaO.js";import{d as $,r as v,c as B,s as G,a as r,b as s,K as _,I as l,ac as H,G as h,F as y,a1 as b,D as K,O as x,P as z,$ as w,o as u}from"./vue-vendor-2E6AJATX.js";import{S as k,_ as D}from"./index-CkIekciI.js";const E={class:"service-support"},L={class:"search-section"},M={class:"search-content"},O={class:"search-title"},P={class:"search-box"},T={class:"content-section"},U={class:"content-container"},W={class:"navigation"},j=["onClick"],A={class:"nav-text"},J={class:"question-list"},R={class:"question-scroll-area"},X=["onClick"],Y={class:"question-title"},Z={class:"question-answer"},ee={class:"answer-content"},se={class:"helpful-section"},te={class:"helpful-buttons"},oe=$({__name:"ServiceSupport",props:{title:{default:"嗨！有什么需要帮忙的吗？"},placeholder:{default:"搜索关键词"},navigationItems:{},onSearch:{},onGetQuestions:{},onHelpfulFeedback:{}},setup(q){const n=q,d=v(""),p=v(0),c=v([]),S=v(!1),I=B(()=>c.value[p.value]||[]),C=()=>{n.onSearch&&d.value.trim()&&n.onSearch(d.value.trim())},N=async t=>{if(p.value=t,!c.value[t]&&n.onGetQuestions){S.value=!0;try{const i=(await n.onGetQuestions(t)).map(m=>({...m,expanded:!1}));c.value[t]=i}catch(e){console.error("获取问题列表失败:",e),c.value[t]=[]}finally{S.value=!1}}},V=t=>{const e=c.value[p.value];e&&e[t]&&(e[t].expanded=!e[t].expanded)},g=(t,e)=>{n.onHelpfulFeedback&&n.onHelpfulFeedback(t,e)};return G(()=>{n.navigationItems.length>0&&N(0)}),(t,e)=>{const i=Q,m=F;return u(),r("div",E,[s("div",L,[s("div",M,[s("p",O,_(t.title||"嗨！有什么需要帮忙的吗？"),1),s("div",P,[l(m,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=o=>d.value=o),placeholder:t.placeholder||"搜索关键词",class:"search-input",onKeyup:H(C,["enter"])},{suffix:h(()=>[l(i,{class:"search-btn",onClick:C},{default:h(()=>[l(k,{iconName:"support-search",className:"search-icon"})]),_:1})]),_:1},8,["modelValue","placeholder"])])])]),s("div",T,[s("div",U,[s("div",W,[(u(!0),r(y,null,b(t.navigationItems,(o,a)=>(u(),r("div",{class:K(["nav-item",{active:p.value===a}]),key:a,onClick:f=>N(a)},[l(k,{iconName:o.icon,className:"nav-icon"},null,8,["iconName"]),s("span",A,_(o.text),1)],10,j))),128))]),s("div",J,[s("div",R,[(u(!0),r(y,null,b(I.value,(o,a)=>(u(),r("div",{class:"question-item",key:a},[s("div",{class:"question-header",onClick:f=>V(a)},[l(k,{iconName:o.expanded?"support-close":"support-open",className:"expand-icon"},null,8,["iconName"]),s("span",Y,_(o.title),1)],8,X),x(s("div",Z,[s("div",ee,_(o.answer),1),s("div",se,[e[3]||(e[3]=s("span",{class:"helpful-text"},"对您是否有帮助？",-1)),s("div",te,[l(i,{size:"small",type:"primary",plain:"",onClick:f=>g(a,!0)},{default:h(()=>e[1]||(e[1]=[w(" 是 ")])),_:2,__:[1]},1032,["onClick"]),l(i,{size:"small",plain:"",onClick:f=>g(a,!1)},{default:h(()=>e[2]||(e[2]=[w(" 否 ")])),_:2,__:[2]},1032,["onClick"])])])],512),[[z,o.expanded]])]))),128))])])])])])}}}),ce=D(oe,[["__scopeId","data-v-41ca98ef"]]);export{ce as S};
