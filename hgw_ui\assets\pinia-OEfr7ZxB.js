import{aj as M,r as V,ai as z,Q as G,j as x,at as B,a2 as $,au as T,ag as tt,t as et,z as st,a4 as nt,c as ot,i as ct,av as rt}from"./vue-vendor-2E6AJATX.js";/*!
 * pinia v3.0.3
 * (c) 2025 <PERSON>
 * @license MIT
 */let D;const E=t=>D=t,J=Symbol();function k(t){return t&&typeof t=="object"&&Object.prototype.toString.call(t)==="[object Object]"&&typeof t.toJSON!="function"}var w;(function(t){t.direct="direct",t.patchObject="patch object",t.patchFunction="patch function"})(w||(w={}));function yt(){const t=M(!0),c=t.run(()=>V({}));let s=[],e=[];const a=z({install(r){E(a),a._a=r,r.provide(J,a),r.config.globalProperties.$pinia=a,e.forEach(u=>s.push(u)),e=[]},use(r){return this._a?s.push(r):e.push(r),this},_p:s,_a:null,_e:t,_s:new Map,state:c});return a}const K=()=>{};function A(t,c,s,e=K){t.push(c);const a=()=>{const r=t.indexOf(c);r>-1&&(t.splice(r,1),e())};return!s&&T()&&tt(a),a}function p(t,...c){t.slice().forEach(s=>{s(...c)})}const ut=t=>t(),H=Symbol(),I=Symbol();function L(t,c){t instanceof Map&&c instanceof Map?c.forEach((s,e)=>t.set(e,s)):t instanceof Set&&c instanceof Set&&c.forEach(t.add,t);for(const s in c){if(!c.hasOwnProperty(s))continue;const e=c[s],a=t[s];k(a)&&k(e)&&t.hasOwnProperty(s)&&!x(e)&&!B(e)?t[s]=L(a,e):t[s]=e}return t}const at=Symbol();function ft(t){return!k(t)||!Object.prototype.hasOwnProperty.call(t,at)}const{assign:h}=Object;function lt(t){return!!(x(t)&&t.effect)}function it(t,c,s,e){const{state:a,actions:r,getters:u}=c,O=s.state.value[t];let y;function b(){O||(s.state.value[t]=a?a():{});const S=nt(s.state.value[t]);return h(S,r,Object.keys(u||{}).reduce((v,_)=>(v[_]=z(ot(()=>{E(s);const m=s._s.get(t);return u[_].call(m,m)})),v),{}))}return y=Q(t,b,c,s,e,!0),y}function Q(t,c,s={},e,a,r){let u;const O=h({actions:{}},s),y={deep:!0};let b,S,v=[],_=[],m;const j=e.state.value[t];!r&&!j&&(e.state.value[t]={}),V({});let W;function N(o){let n;b=S=!1,typeof o=="function"?(o(e.state.value[t]),n={type:w.patchFunction,storeId:t,events:m}):(L(e.state.value[t],o),n={type:w.patchObject,payload:o,storeId:t,events:m});const f=W=Symbol();st().then(()=>{W===f&&(b=!0)}),S=!0,p(v,n,e.state.value[t])}const q=r?function(){const{state:n}=s,f=n?n():{};this.$patch(d=>{h(d,f)})}:K;function U(){u.stop(),v=[],_=[],e._s.delete(t)}const F=(o,n="")=>{if(H in o)return o[I]=n,o;const f=function(){E(e);const d=Array.from(arguments),C=[],R=[];function Y(l){C.push(l)}function Z(l){R.push(l)}p(_,{args:d,name:f[I],store:i,after:Y,onError:Z});let g;try{g=o.apply(this&&this.$id===t?this:i,d)}catch(l){throw p(R,l),l}return g instanceof Promise?g.then(l=>(p(C,l),l)).catch(l=>(p(R,l),Promise.reject(l))):(p(C,g),g)};return f[H]=!0,f[I]=n,f},X={_p:e,$id:t,$onAction:A.bind(null,_),$patch:N,$reset:q,$subscribe(o,n={}){const f=A(v,o,n.detached,()=>d()),d=u.run(()=>et(()=>e.state.value[t],C=>{(n.flush==="sync"?S:b)&&o({storeId:t,type:w.direct,events:m},C)},h({},y,n)));return f},$dispose:U},i=G(X);e._s.set(t,i);const P=(e._a&&e._a.runWithContext||ut)(()=>e._e.run(()=>(u=M()).run(()=>c({action:F}))));for(const o in P){const n=P[o];if(x(n)&&!lt(n)||B(n))r||(j&&ft(n)&&(x(n)?n.value=j[o]:L(n,j[o])),e.state.value[t][o]=n);else if(typeof n=="function"){const f=F(n,o);P[o]=f,O.actions[o]=n}}return h(i,P),h($(i),P),Object.defineProperty(i,"$state",{get:()=>e.state.value[t],set:o=>{N(n=>{h(n,o)})}}),e._p.forEach(o=>{h(i,u.run(()=>o({store:i,app:e._a,pinia:e,options:O})))}),j&&r&&s.hydrate&&s.hydrate(i.$state,j),b=!0,S=!0,i}/*! #__NO_SIDE_EFFECTS__ */function St(t,c,s){let e;const a=typeof c=="function";e=a?s:c;function r(u,O){const y=rt();return u=u||(y?ct(J,null):null),u&&E(u),u=D,u._s.has(t)||(a?Q(t,c,e,u):it(t,e,u)),u._s.get(t)}return r.$id=t,r}export{yt as c,St as d};
